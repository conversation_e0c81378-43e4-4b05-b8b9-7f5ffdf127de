# Table of contents

-   [Introduction](README.md)
-   [Get Started](getting-started/README.md)
-   [Universo Platformo Features](universo-platformo/README.md)
    -   [UPDL Node System](universo-platformo/updl-nodes/README.md)
        -   [Space Nodes](universo-platformo/updl-nodes/space-nodes.md)
        -   [Entity <PERSON>](universo-platformo/updl-nodes/entity-nodes.md)
        -   [Conditional Parameters](universo-platformo/updl-nodes/conditional-parameters.md)
        -   [Component Nodes](universo-platformo/updl-nodes/component-nodes.md)
        -   [Action Nodes](universo-platformo/updl-nodes/action-nodes.md)
        -   [Event Nodes](universo-platformo/updl-nodes/event-nodes.md)
        -   [Data Nodes](universo-platformo/updl-nodes/data-nodes.md)
    -   [MMOOMM Templates](universo-platformo/mmoomm-templates/README.md)
        -   [PlayCanvas MMOOMM](universo-platformo/mmoomm-templates/playcanvas-mmoomm.md)
        -   [AR.js Templates](universo-platformo/mmoomm-templates/arjs-templates.md)
        -   [A-Frame Templates](universo-platformo/mmoomm-templates/aframe-templates.md)
    -   [Multi-Platform Export](universo-platformo/export/README.md)
        -   [AR.js Export](universo-platformo/export/arjs-export.md)
        -   [PlayCanvas Export](universo-platformo/export/playcanvas-export.md)
        -   [A-Frame Export](universo-platformo/export/aframe-export.md)
    -   [Enhanced Resource Systems](universo-platformo/resources/README.md)
        -   [Resource Management](universo-platformo/resources/resource-management.md)
        -   [Trading Systems](universo-platformo/resources/trading-systems.md)
        -   [Mining Mechanics](universo-platformo/resources/mining-mechanics.md)
-   [Roadmap](roadmap/README.md)
    -   [Current Architecture](roadmap/current-architecture/README.md)
        -   [Existing Applications](roadmap/current-architecture/existing-apps.md)
        -   [Packages Analysis](roadmap/current-architecture/packages-analysis.md)
        -   [Integration Patterns](roadmap/current-architecture/integration-patterns.md)
    -   [Target Architecture](roadmap/target-architecture/README.md)
        -   [MMOOMM Applications](roadmap/target-architecture/mmoomm-apps.md)
        -   [Core Platform Applications](roadmap/target-architecture/core-platform-apps.md)
        -   [Microservices Design](roadmap/target-architecture/microservices-design.md)
        -   [Orchestration Strategy](roadmap/target-architecture/orchestration-strategy.md)
    -   [Implementation Plan](roadmap/implementation-plan/README.md)
        -   [Phase 1: MVP](roadmap/implementation-plan/phase-1-mvp.md)
        -   [Phase 2: Core Systems](roadmap/implementation-plan/phase-2-core.md)
        -   [Phase 3: Advanced Features](roadmap/implementation-plan/phase-3-advanced.md)
        -   [Phase 4: Ecosystem](roadmap/implementation-plan/phase-4-ecosystem.md)
        -   [Migration Strategy](roadmap/implementation-plan/migration-strategy.md)
    -   [Applications](roadmap/applications/README.md)
        -   [Game Mechanics](roadmap/applications/game-mechanics/README.md)
        -   [Social Systems](roadmap/applications/social-systems/README.md)
        -   [Technical Systems](roadmap/applications/technical-systems/README.md)
        -   [Platform Core](roadmap/applications/platform-core/README.md)
    -   [Technical Specifications](roadmap/technical-specifications/README.md)
        -   [Database Design](roadmap/technical-specifications/database-design.md)
        -   [API Specifications](roadmap/technical-specifications/api-specifications.md)
        -   [Security Requirements](roadmap/technical-specifications/security-requirements.md)
        -   [Performance Targets](roadmap/technical-specifications/performance-targets.md)
    -   [Milestones](roadmap/milestones/README.md)
        -   [v0.22.0-alpha](roadmap/milestones/v0.22.0-alpha.md)
        -   [v0.25.0-beta](roadmap/milestones/v0.25.0-beta.md)
        -   [v1.0.0-release](roadmap/milestones/v1.0.0-release.md)
        -   [Future Versions](roadmap/milestones/future-versions.md)
-   [Applications](applications/README.md)
    -   [UPDL System](applications/updl/README.md)
    -   [Publication System](applications/publish/README.md)
    -   [Profile Management](applications/profile/README.md)
    -   [Analytics System](applications/analytics/README.md)
    -   [Authentication System](applications/auth/README.md)
-   [Contribution Guide](contributing/README.md)
    -   [Building Node](contributing/building-node.md)
-   [API Reference](api-reference/README.md)
    -   [Assistants](api-reference/assistants.md)
    -   [Attachments](api-reference/attachments.md)
    -   [Chat Message](api-reference/chat-message.md)
    -   [Chatflows](api-reference/chatflows.md)
    -   [Document Store](api-reference/document-store.md)
    -   [Feedback](api-reference/feedback.md)
    -   [Leads](api-reference/leads.md)
    -   [Ping](api-reference/ping.md)
    -   [Prediction](api-reference/prediction.md)
    -   [Tools](api-reference/tools.md)
    -   [Upsert History](api-reference/upsert-history.md)
    -   [Variables](api-reference/variables.md)
    -   [Vector Upsert](api-reference/vector-upsert.md)
-   [CLI Reference](cli-reference/README.md)
    -   [User](cli-reference/user.md)
-   [Using Universo Platformo](using-flowise/README.md)
    -   [Agentflow V2](using-flowise/agentflowv2.md)
    -   [Agentflow V1 (Deprecating)](using-flowise/agentflowv1/README.md)
        -   [Multi-Agents](using-flowise/agentflowv1/multi-agents.md)
        -   [Sequential Agents](using-flowise/agentflowv1/sequential-agents/README.md)
            -   [Video Tutorials](using-flowise/agentflowv1/sequential-agents/video-tutorials.md)
    -   [Prediction](using-flowise/prediction.md)
    -   [Streaming](using-flowise/streaming.md)
    -   [Document Stores](using-flowise/document-stores.md)
    -   [Upsertion](using-flowise/upsertion.md)
    -   [Analytic](using-flowise/analytics/README.md)
        -   [Arize](using-flowise/analytics/arize.md)
        -   [Langfuse](using-flowise/analytics/langfuse.md)
        -   [Lunary](using-flowise/analytics/lunary.md)
        -   [Opik](using-flowise/analytics/opik.md)
        -   [Phoenix](using-flowise/analytics/phoenix.md)
    -   [Monitoring](using-flowise/monitoring.md)
    -   [Embed](using-flowise/embed.md)
    -   [Uploads](using-flowise/uploads.md)
    -   [Variables](using-flowise/variables.md)
    -   [Workspaces](using-flowise/workspaces.md)
    -   [Evaluations](using-flowise/evaluations.md)
-   [Configuration](configuration/README.md)
    -   [Auth](configuration/authorization/README.md)
        -   [Application](configuration/authorization/app-level.md)
        -   [Flows](configuration/authorization/chatflow-level.md)
    -   [Databases](configuration/databases.md)
    -   [Deployment](configuration/deployment/README.md)
        -   [AWS](configuration/deployment/aws.md)
        -   [Azure](configuration/deployment/azure.md)
        -   [Alibaba Cloud](https://aliyun-computenest.github.io/quickstart-flowise/)
        -   [Digital Ocean](configuration/deployment/digital-ocean.md)
        -   [Elestio](https://elest.io/open-source/flowiseai)
        -   [GCP](configuration/deployment/gcp.md)
        -   [Hugging Face](configuration/deployment/hugging-face.md)
        -   [Kubernetes using Helm](https://artifacthub.io/packages/helm/cowboysysop/flowise)
        -   [Railway](configuration/deployment/railway.md)
        -   [Render](configuration/deployment/render.md)
        -   [Replit](configuration/deployment/replit.md)
        -   [RepoCloud](https://repocloud.io/details/?app_id=29)
        -   [Sealos](configuration/deployment/sealos.md)
        -   [Zeabur](configuration/deployment/zeabur.md)
    -   [Environment Variables](configuration/environment-variables.md)
    -   [Rate Limit](configuration/rate-limit.md)
    -   [Running Flowise behind company proxy](configuration/running-flowise-behind-company-proxy.md)
    -   [SSO](configuration/sso.md)
    -   [Running Flowise using Queue](configuration/running-flowise-using-queue.md)
    -   [Running in Production](configuration/running-in-production.md)
-   [Integrations](integrations/README.md)
    -   [LangChain](integrations/langchain/README.md)
        -   [Agents](integrations/langchain/agents/README.md)
            -   [Airtable Agent](integrations/langchain/agents/airtable-agent.md)
            -   [AutoGPT](integrations/langchain/agents/autogpt.md)
            -   [BabyAGI](integrations/langchain/agents/babyagi.md)
            -   [CSV Agent](integrations/langchain/agents/csv-agent.md)
            -   [Conversational Agent](integrations/langchain/agents/conversational-agent.md)
            -   [Conversational Retrieval Agent](integrations/langchain/agents/conversational-retrieval-agent.md)
            -   [MistralAI Tool Agent](integrations/langchain/agents/mistralai-tool-agent.md)
            -   [OpenAI Assistant](integrations/langchain/agents/openai-assistant/README.md)
                -   [Threads](integrations/langchain/agents/openai-assistant/threads.md)
            -   [OpenAI Function Agent](integrations/langchain/agents/openai-function-agent.md)
            -   [OpenAI Tool Agent](integrations/langchain/agents/openai-tool-agent.md)
            -   [ReAct Agent Chat](integrations/langchain/agents/react-agent-chat.md)
            -   [ReAct Agent LLM](integrations/langchain/agents/react-agent-llm.md)
            -   [Tool Agent](integrations/langchain/agents/tool-agent.md)
            -   [XML Agent](integrations/langchain/agents/xml-agent.md)
        -   [Cache](integrations/langchain/cache/README.md)
            -   [InMemory Cache](integrations/langchain/cache/in-memory-cache.md)
            -   [InMemory Embedding Cache](integrations/langchain/cache/inmemory-embedding-cache.md)
            -   [Momento Cache](integrations/langchain/cache/momento-cache.md)
            -   [Redis Cache](integrations/langchain/cache/redis-cache.md)
            -   [Redis Embeddings Cache](integrations/langchain/cache/redis-embeddings-cache.md)
            -   [Upstash Redis Cache](integrations/langchain/cache/upstash-redis-cache.md)
        -   [Chains](integrations/langchain/chains/README.md)
            -   [GET API Chain](integrations/langchain/chains/get-api-chain.md)
            -   [OpenAPI Chain](integrations/langchain/chains/openapi-chain.md)
            -   [POST API Chain](integrations/langchain/chains/post-api-chain.md)
            -   [Conversation Chain](integrations/langchain/chains/conversation-chain.md)
            -   [Conversational Retrieval QA Chain](integrations/langchain/chains/conversational-retrieval-qa-chain.md)
            -   [LLM Chain](integrations/langchain/chains/llm-chain.md)
            -   [Multi Prompt Chain](integrations/langchain/chains/multi-prompt-chain.md)
            -   [Multi Retrieval QA Chain](integrations/langchain/chains/multi-retrieval-qa-chain.md)
            -   [Retrieval QA Chain](integrations/langchain/chains/retrieval-qa-chain.md)
            -   [Sql Database Chain](integrations/langchain/chains/sql-database-chain.md)
            -   [Vectara QA Chain](integrations/langchain/chains/vectara-chain.md)
            -   [VectorDB QA Chain](integrations/langchain/chains/vectordb-qa-chain.md)
        -   [Chat Models](integrations/langchain/chat-models/README.md)
            -   [AWS ChatBedrock](integrations/langchain/chat-models/aws-chatbedrock.md)
            -   [Azure ChatOpenAI](integrations/langchain/chat-models/azure-chatopenai-1.md)
            -   [NVIDIA NIM](integrations/langchain/chat-models/nvidia-nim.md)
            -   [ChatAnthropic](integrations/langchain/chat-models/chatanthropic.md)
            -   [ChatCohere](integrations/langchain/chat-models/chatcohere.md)
            -   [Chat Fireworks](integrations/langchain/chat-models/chat-fireworks.md)
            -   [ChatGoogleGenerativeAI](integrations/langchain/chat-models/google-ai.md)
            -   [Google VertexAI](integrations/langchain/chat-models/google-vertexai.md)
            -   [ChatHuggingFace](integrations/langchain/chat-models/chathuggingface.md)
            -   [ChatLocalAI](integrations/langchain/chat-models/chatlocalai.md)
            -   [ChatMistralAI](integrations/langchain/chat-models/mistral-ai.md)
            -   [IBM Watsonx](integrations/langchain/chat-models/ibm-watsonx.md)
            -   [ChatOllama](integrations/langchain/chat-models/chatollama.md)
            -   [ChatOpenAI](integrations/langchain/chat-models/azure-chatopenai.md)
            -   [ChatTogetherAI](integrations/langchain/chat-models/chattogetherai.md)
            -   [GroqChat](integrations/langchain/chat-models/groqchat.md)
        -   [Document Loaders](integrations/langchain/document-loaders/README.md)
            -   [Airtable](integrations/langchain/document-loaders/airtable.md)
            -   [API Loader](integrations/langchain/document-loaders/api-loader.md)
            -   [Apify Website Content Crawler](integrations/langchain/document-loaders/apify-website-content-crawler.md)
            -   [BraveSearch Loader](integrations/langchain/document-loaders/bravesearch-api.md)
            -   [Cheerio Web Scraper](integrations/langchain/document-loaders/cheerio-web-scraper.md)
            -   [Confluence](integrations/langchain/document-loaders/confluence.md)
            -   [Csv File](integrations/langchain/document-loaders/csv-file.md)
            -   [Custom Document Loader](integrations/langchain/document-loaders/custom-document-loader.md)
            -   [Document Store](integrations/langchain/document-loaders/document-store.md)
            -   [Docx File](integrations/langchain/document-loaders/docx-file.md)
            -   [Epub File](integrations/langchain/document-loaders/epub-file.md)
            -   [Figma](integrations/langchain/document-loaders/figma.md)
            -   [File](integrations/langchain/document-loaders/file-loader.md)
            -   [FireCrawl](integrations/langchain/document-loaders/firecrawl.md)
            -   [Folder](integrations/langchain/document-loaders/folder.md)
            -   [GitBook](integrations/langchain/document-loaders/gitbook.md)
            -   [Github](integrations/langchain/document-loaders/github.md)
            -   [Google Drive](integrations/langchain/document-loaders/google-drive.md)
            -   [Google Sheets](integrations/langchain/document-loaders/google-sheets.md)
            -   [Jira](integrations/langchain/document-loaders/jira.md)
            -   [Json File](integrations/langchain/document-loaders/json-file.md)
            -   [Json Lines File](integrations/langchain/document-loaders/jsonlines.md)
            -   [Microsoft Excel](integrations/langchain/document-loaders/microsoft-excel.md)
            -   [Microsoft Powerpoint](integrations/langchain/document-loaders/microsoft-powerpoint.md)
            -   [Microsoft Word](integrations/langchain/document-loaders/microsoft-word.md)
            -   [Notion](integrations/langchain/document-loaders/notion.md)
            -   [Oxylabs](integrations/langchain/document-loaders/oxylabs.md)
            -   [PDF Files](integrations/langchain/document-loaders/pdf-file.md)
            -   [Plain Text](integrations/langchain/document-loaders/plain-text.md)
            -   [Playwright Web Scraper](integrations/langchain/document-loaders/playwright-web-scraper.md)
            -   [Puppeteer Web Scraper](integrations/langchain/document-loaders/puppeteer-web-scraper.md)
            -   [S3 File Loader](integrations/langchain/document-loaders/s3-file-loader.md)
            -   [SearchApi For Web Search](integrations/langchain/document-loaders/searchapi-for-web-search.md)
            -   [SerpApi For Web Search](integrations/langchain/document-loaders/serpapi-for-web-search.md)
            -   [Spider - web search & crawler](integrations/langchain/document-loaders/spider-web-scraper-crawler.md)
            -   [Text File](integrations/langchain/document-loaders/text-file.md)
            -   [Unstructured File Loader](integrations/langchain/document-loaders/unstructured-file-loader.md)
            -   [Unstructured Folder Loader](integrations/langchain/document-loaders/unstructured-folder-loader.md)
        -   [Embeddings](integrations/langchain/embeddings/README.md)
            -   [AWS Bedrock Embeddings](integrations/langchain/embeddings/aws-bedrock-embeddings.md)
            -   [Azure OpenAI Embeddings](integrations/langchain/embeddings/azure-openai-embeddings.md)
            -   [Cohere Embeddings](integrations/langchain/embeddings/cohere-embeddings.md)
            -   [Google GenerativeAI Embeddings](integrations/langchain/embeddings/googlegenerativeai-embeddings.md)
            -   [Google VertexAI Embeddings](integrations/langchain/embeddings/googlevertexai-embeddings.md)
            -   [HuggingFace Inference Embeddings](integrations/langchain/embeddings/huggingface-inference-embeddings.md)
            -   [LocalAI Embeddings](integrations/langchain/embeddings/localai-embeddings.md)
            -   [MistralAI Embeddings](integrations/langchain/embeddings/mistralai-embeddings.md)
            -   [Ollama Embeddings](integrations/langchain/embeddings/ollama-embeddings.md)
            -   [OpenAI Embeddings](integrations/langchain/embeddings/openai-embeddings.md)
            -   [OpenAI Embeddings Custom](integrations/langchain/embeddings/openai-embeddings-custom.md)
            -   [TogetherAI Embedding](integrations/langchain/embeddings/togetherai-embedding.md)
            -   [VoyageAI Embeddings](integrations/langchain/embeddings/voyageai-embeddings.md)
        -   [LLMs](integrations/langchain/llms/README.md)
            -   [AWS Bedrock](integrations/langchain/llms/aws-bedrock.md)
            -   [Azure OpenAI](integrations/langchain/llms/azure-openai.md)
            -   [Cohere](integrations/langchain/llms/cohere.md)
            -   [GoogleVertex AI](integrations/langchain/llms/googlevertex-ai.md)
            -   [HuggingFace Inference](integrations/langchain/llms/huggingface-inference.md)
            -   [Ollama](integrations/langchain/llms/ollama.md)
            -   [OpenAI](integrations/langchain/llms/openai.md)
            -   [Replicate](integrations/langchain/llms/replicate.md)
        -   [Memory](integrations/langchain/memory/README.md)
            -   [Buffer Memory](integrations/langchain/memory/buffer-memory.md)
            -   [Buffer Window Memory](integrations/langchain/memory/buffer-window-memory.md)
            -   [Conversation Summary Memory](integrations/langchain/memory/conversation-summary-memory.md)
            -   [Conversation Summary Buffer Memory](integrations/langchain/memory/conversation-summary-buffer-memory.md)
            -   [DynamoDB Chat Memory](integrations/langchain/memory/dynamodb-chat-memory.md)
            -   [MongoDB Atlas Chat Memory](integrations/langchain/memory/mongodb-atlas-chat-memory.md)
            -   [Redis-Backed Chat Memory](integrations/langchain/memory/redis-backed-chat-memory.md)
            -   [Upstash Redis-Backed Chat Memory](integrations/langchain/memory/upstash-redis-backed-chat-memory.md)
            -   [Zep Memory](integrations/langchain/memory/zep-memory.md)
        -   [Moderation](integrations/langchain/moderation/README.md)
            -   [OpenAI Moderation](integrations/langchain/moderation/openai-moderation.md)
            -   [Simple Prompt Moderation](integrations/langchain/moderation/simple-prompt-moderation.md)
        -   [Output Parsers](integrations/langchain/output-parsers/README.md)
            -   [CSV Output Parser](integrations/langchain/output-parsers/csv-output-parser.md)
            -   [Custom List Output Parser](integrations/langchain/output-parsers/custom-list-output-parser.md)
            -   [Structured Output Parser](integrations/langchain/output-parsers/structured-output-parser.md)
            -   [Advanced Structured Output Parser](integrations/langchain/output-parsers/advanced-structured-output-parser.md)
        -   [Prompts](integrations/langchain/prompts/README.md)
            -   [Chat Prompt Template](integrations/langchain/prompts/chat-prompt-template.md)
            -   [Few Shot Prompt Template](integrations/langchain/prompts/few-shot-prompt-template.md)
            -   [Prompt Template](integrations/langchain/prompts/prompt-template.md)
        -   [Record Managers](integrations/langchain/record-managers.md)
        -   [Retrievers](integrations/langchain/retrievers/README.md)
            -   [Extract Metadata Retriever](integrations/langchain/retrievers/extract-metadata-retriever.md)
            -   [Custom Retriever](integrations/langchain/retrievers/custom-retriever.md)
            -   [Cohere Rerank Retriever](integrations/langchain/retrievers/cohere-rerank-retriever.md)
            -   [Embeddings Filter Retriever](integrations/langchain/retrievers/embeddings-filter-retriever.md)
            -   [HyDE Retriever](integrations/langchain/retrievers/hyde-retriever.md)
            -   [LLM Filter Retriever](integrations/langchain/retrievers/llm-filter-retriever.md)
            -   [Multi Query Retriever](integrations/langchain/retrievers/multi-query-retriever.md)
            -   [Prompt Retriever](integrations/langchain/retrievers/prompt-retriever.md)
            -   [Reciprocal Rank Fusion Retriever](integrations/langchain/retrievers/reciprocal-rank-fusion-retriever.md)
            -   [Similarity Score Threshold Retriever](integrations/langchain/retrievers/similarity-score-threshold-retriever.md)
            -   [Vector Store Retriever](integrations/langchain/retrievers/vector-store-retriever.md)
            -   [Voyage AI Rerank Retriever](integrations/langchain/retrievers/page.md)
        -   [Text Splitters](integrations/langchain/text-splitters/README.md)
            -   [Character Text Splitter](integrations/langchain/text-splitters/character-text-splitter.md)
            -   [Code Text Splitter](integrations/langchain/text-splitters/code-text-splitter.md)
            -   [Html-To-Markdown Text Splitter](integrations/langchain/text-splitters/html-to-markdown-text-splitter.md)
            -   [Markdown Text Splitter](integrations/langchain/text-splitters/markdown-text-splitter.md)
            -   [Recursive Character Text Splitter](integrations/langchain/text-splitters/recursive-character-text-splitter.md)
            -   [Token Text Splitter](integrations/langchain/text-splitters/token-text-splitter.md)
        -   [Tools](integrations/langchain/tools/README.md)
            -   [BraveSearch API](integrations/langchain/tools/bravesearch-api.md)
            -   [Calculator](integrations/langchain/tools/calculator.md)
            -   [Chain Tool](integrations/langchain/tools/chain-tool.md)
            -   [Chatflow Tool](integrations/langchain/tools/chatflow-tool.md)
            -   [Custom Tool](integrations/langchain/tools/custom-tool.md)
            -   [Exa Search](integrations/langchain/tools/exa-search.md)
            -   [Gmail](integrations/langchain/tools/gmail.md)
            -   [Google Calendar](integrations/langchain/tools/google-calendar.md)
            -   [Google Custom Search](integrations/langchain/tools/google-custom-search.md)
            -   [Google Drive](integrations/langchain/tools/google-drive.md)
            -   [Google Sheets](integrations/langchain/tools/google-sheets.md)
            -   [Microsoft Outlook](integrations/langchain/tools/microsoft-outlook.md)
            -   [Microsoft Teams](integrations/langchain/tools/microsoft-teams.md)
            -   [OpenAPI Toolkit](integrations/langchain/tools/openapi-toolkit.md)
            -   [Code Interpreter by E2B](integrations/langchain/tools/python-interpreter.md)
            -   [Read File](integrations/langchain/tools/read-file.md)
            -   [Request Get](integrations/langchain/tools/request-get.md)
            -   [Request Post](integrations/langchain/tools/request-post.md)
            -   [Retriever Tool](integrations/langchain/tools/retriever-tool.md)
            -   [SearchApi](integrations/langchain/tools/searchapi.md)
            -   [SearXNG](integrations/langchain/tools/searxng.md)
            -   [Serp API](integrations/langchain/tools/serp-api.md)
            -   [Serper](integrations/langchain/tools/serper.md)
            -   [Tavily](integrations/langchain/tools/tavily-ai.md)
            -   [Web Browser](integrations/langchain/tools/web-browser.md)
            -   [Write File](integrations/langchain/tools/write-file.md)
        -   [Vector Stores](integrations/langchain/vector-stores/README.md)
            -   [AstraDB](integrations/langchain/vector-stores/astradb.md)
            -   [Chroma](integrations/langchain/vector-stores/chroma.md)
            -   [Couchbase](integrations/langchain/vector-stores/couchbase.md)
            -   [Elastic](integrations/langchain/vector-stores/elastic.md)
            -   [Faiss](integrations/langchain/vector-stores/faiss.md)
            -   [In-Memory Vector Store](integrations/langchain/vector-stores/in-memory-vector-store.md)
            -   [Milvus](integrations/langchain/vector-stores/milvus.md)
            -   [MongoDB Atlas](integrations/langchain/vector-stores/mongodb-atlas.md)
            -   [OpenSearch](integrations/langchain/vector-stores/opensearch.md)
            -   [Pinecone](integrations/langchain/vector-stores/pinecone.md)
            -   [Postgres](integrations/langchain/vector-stores/postgres.md)
            -   [Qdrant](integrations/langchain/vector-stores/qdrant.md)
            -   [Redis](integrations/langchain/vector-stores/redis.md)
            -   [SingleStore](integrations/langchain/vector-stores/singlestore.md)
            -   [Supabase](integrations/langchain/vector-stores/supabase.md)
            -   [Upstash Vector](integrations/langchain/vector-stores/upstash-vector.md)
            -   [Vectara](integrations/langchain/vector-stores/vectara.md)
            -   [Weaviate](integrations/langchain/vector-stores/weaviate.md)
            -   [Zep Collection - Open Source](integrations/langchain/vector-stores/zep-collection-open-source.md)
            -   [Zep Collection - Cloud](integrations/langchain/vector-stores/zep-collection-cloud.md)
    -   [LiteLLM Proxy](integrations/litellm/README.md)
    -   [LlamaIndex](integrations/llamaindex/README.md)
        -   [Agents](integrations/llamaindex/agents/README.md)
            -   [OpenAI Tool Agent](integrations/llamaindex/agents/openai-tool-agent.md)
            -   [Anthropic Tool Agent](integrations/llamaindex/agents/openai-tool-agent-1.md)
        -   [Chat Models](integrations/llamaindex/chat-models/README.md)
            -   [AzureChatOpenAI](integrations/llamaindex/chat-models/azurechatopenai.md)
            -   [ChatAnthropic](integrations/llamaindex/chat-models/chatanthropic.md)
            -   [ChatMistral](integrations/llamaindex/chat-models/chatmistral.md)
            -   [ChatOllama](integrations/llamaindex/chat-models/chatollama.md)
            -   [ChatOpenAI](integrations/llamaindex/chat-models/chatopenai.md)
            -   [ChatTogetherAI](integrations/llamaindex/chat-models/chattogetherai.md)
            -   [ChatGroq](integrations/llamaindex/chat-models/chatgroq.md)
        -   [Embeddings](integrations/llamaindex/embeddings/README.md)
            -   [Azure OpenAI Embeddings](integrations/llamaindex/embeddings/azure-openai-embeddings.md)
            -   [OpenAI Embedding](integrations/llamaindex/embeddings/openai-embedding.md)
        -   [Engine](integrations/llamaindex/engine/README.md)
            -   [Query Engine](integrations/llamaindex/engine/query-engine.md)
            -   [Simple Chat Engine](integrations/llamaindex/engine/simple-chat-engine.md)
            -   [Context Chat Engine](integrations/llamaindex/engine/context-chat-engine.md)
            -   [Sub-Question Query Engine](integrations/llamaindex/engine/sub-question-query-engine.md)
        -   [Response Synthesizer](integrations/llamaindex/response-synthesizer/README.md)
            -   [Refine](integrations/llamaindex/response-synthesizer/refine.md)
            -   [Compact And Refine](integrations/llamaindex/response-synthesizer/compact-and-refine.md)
            -   [Simple Response Builder](integrations/llamaindex/response-synthesizer/simple-response-builder.md)
            -   [Tree Summarize](integrations/llamaindex/response-synthesizer/tree-summarize.md)
        -   [Tools](integrations/llamaindex/tools/README.md)
            -   [Query Engine Tool](integrations/llamaindex/tools/query-engine-tool.md)
        -   [Vector Stores](integrations/llamaindex/vector-stores/README.md)
            -   [Pinecone](integrations/llamaindex/vector-stores/pinecone.md)
            -   [SimpleStore](integrations/llamaindex/vector-stores/queryengine-tool.md)
    -   [Utilities](integrations/utilities/README.md)
        -   [Custom JS Function](integrations/utilities/custom-js-function.md)
        -   [Set/Get Variable](integrations/utilities/set-get-variable.md)
        -   [If Else](integrations/utilities/if-else.md)
        -   [Sticky Note](integrations/utilities/sticky-note.md)
    -   [External Integrations](integrations/3rd-party-platform-integration/README.md)
        -   [Zapier Zaps](integrations/3rd-party-platform-integration/zapier-zaps.md)
        -   [Open WebUI](integrations/3rd-party-platform-integration/open-webui.md)
        -   [Streamlit](integrations/3rd-party-platform-integration/streamlit.md)
-   [Migration Guide](migration-guide/README.md)
    -   [Cloud Migration](migration-guide/cloud-migration.md)
    -   [v1.3.0 Migration Guide](migration-guide/v1.3.0-migration-guide.md)
    -   [v1.4.3 Migration Guide](migration-guide/v1.4.3-migration-guide.md)
    -   [v2.1.4 Migration Guide](migration-guide/v2.1.4-migration-guide.md)
-   [Tutorials](tutorials/README.md)
    -   [RAG](tutorials/rag.md)
    -   [Agentic RAG](tutorials/agentic-rag.md)
    -   [SQL Agent](tutorials/sql-agent.md)
    -   [Agent as Tool](tutorials/agent-as-tool.md)
    -   [Interacting with API](tutorials/interacting-with-api.md)
    -   [Tools & MCP](tutorials/tools-and-mcp.md)
    -   [Structured Output](tutorials/structured-output.md)
    -   [Human In The Loop](tutorials/human-in-the-loop.md)
    -   [Deep Research](tutorials/deep-research.md)
    -   [Customer Support](tutorials/customer-support.md)
    -   [Supervisor and Workers](tutorials/supervisor-and-workers.md)

## Flowise

-   [Flowise GitHub](https://github.com/FlowiseAI)
-   [Flowise Cloud](https://flowiseai.com/join)
