---
description: En v1.3.0, introducimos Credentials
---

# v1.3.0 Migration Guide

***

Las Credentials permiten al usuario almacenar todas las API keys de terceros en la base de datos, y pueden ser reutilizadas fácilmente en los nodos respectivos, sin tener que copiar y pegar cada vez.

Las Credentials se cifran mediante una clave de cifrado creada usando una passphrase. Solo el usuario que tiene acceso a la clave puede cifrar/descifrar las credenciales. Además, las credenciales descifradas nunca se enviarán de vuelta al cliente para evitar la suplantación de red.

A continuación, algunas guías importantes para ayudarte a migrar a v1.3.0:

1. Configura la variable de entorno `PASSPHRASE`. Esta se usa para generar una clave de cifrado utilizada para cifrar/descifrar tus credenciales
2. Configura la variable de entorno `SECRETKEY_PATH`. Para persistir tu clave de cifrado, especifica la ubicación donde se guardará la clave de cifrado.

Un archivo `.env` típico debería verse así:

```sh
PORT=3000
PASSPHRASE=MYPASSPHRASE
DATABASE_PATH=/root/.flowise
SECRETKEY_PATH=/root/.flowise
LOG_PATH=/root/.flowise/logs
```

3. Versión del nodo. Se mostrará un mensaje de advertencia en la parte superior derecha de un nodo si la versión está desactualizada. Esto significa que hay nuevos cambios en el nodo, y deberías eliminarlo y volver a agregarlo desde la lista del menú.

<figure><img src="../.gitbook/assets/image (11) (1) (1) (1) (1) (1) (1).png" alt="" width="312"><figcaption></figcaption></figure>

¡Eso es todo! Háznoslo saber si te encuentras con algún problema. ¡Feliz actualización!

## Tutorial en Video

En este tutorial en video, Leon muestra cómo configurar las credentials en Flowise.

{% embed url="https://youtu.be/32DFgPdYMcs" %}
