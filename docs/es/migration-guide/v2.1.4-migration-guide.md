# v2.1.4 Migration Guide

OverrideConfig permite a los usuarios sobrescribir las configuraciones del flujo desde la API o cuando se usa Embed. Debido a preocupaciones de seguridad, ahora está deshabilitado por defecto.

Los usuarios deben especificar explícitamente qué configuraciones pueden ser sobrescritas desde la UI.

1.) Ve a Configuration:

<figure><img src="../.gitbook/assets/image (189).png" alt="" width="221"><figcaption></figcaption></figure>

2.) Habilita Override Configuration:

<figure><img src="../.gitbook/assets/image (190).png" alt=""><figcaption></figcaption></figure>

3.) Activa el interruptor para la configuración que puede ser sobrescrita y guárdala.

<figure><img src="../.gitbook/assets/image (191).png" alt=""><figcaption></figcaption></figure>

4.) Por ejemplo, los usuarios pueden entonces sobrescribir estas variables y configuraciones. Consulta [OverrideConfig](../using-flowise/api.md#override-config).

```json
{
    "overrideConfig": {
        "systemMessage": "You are helpful assistant",
        "vars": {
            "character": "nice"
        }
    }
}
```
