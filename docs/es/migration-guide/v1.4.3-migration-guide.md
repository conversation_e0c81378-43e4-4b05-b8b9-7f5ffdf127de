---
description: En v1.4.3, introducimos un nodo Vector Store unificado
---

# v1.4.3 Migration Guide

***

## Antes

Anteriormente, los usuarios tenían que crear 2 flujos para realizar upsert y consultas:

### Upsert

<figure><img src="../.gitbook/assets/image (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1).png" alt=""><figcaption></figcaption></figure>

### Cargar Existente

<figure><img src="../.gitbook/assets/image (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1).png" alt=""><figcaption></figcaption></figure>

Con esta técnica, hay 2 desventajas:

* Se necesita hacer una llamada adicional al LLM para que ocurra el upsert
* Cualquier cambio pequeño causará que el flujo se vuelva a insertar

## Después

Ahora, el usuario puede usar un solo nodo para lograr todo:

<figure><img src="../.gitbook/assets/image (2) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1).png" alt=""><figcaption></figcaption></figure>

Los usuarios ahora tienen la opción de iniciar manualmente el upsert haciendo clic en el botón **verde** en la parte superior derecha:

<figure><img src="../.gitbook/assets/image (5) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (2) (1) (1).png" alt=""><figcaption></figcaption></figure>

<figure><img src="../.gitbook/assets/image (3) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1).png" alt=""><figcaption></figcaption></figure>

Viene con la nueva API - `/api/v1/vector/upsert`:

<figure><img src="../.gitbook/assets/image (4) (1) (1) (1) (1) (1) (1) (1) (1) (1) (1) (2).png" alt=""><figcaption></figcaption></figure>

En el futuro, implementaremos funciones para consultar y eliminar índices. Este es el primer paso hacia una forma más flexible de realizar operaciones relacionadas con vectores. Recomendamos encarecidamente a los usuarios que actualicen a los nuevos nodos.
