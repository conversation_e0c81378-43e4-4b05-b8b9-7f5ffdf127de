---
description: Aprende sobre las utilidades disponibles en Flowise
---

# Utilities

Las utilidades son componentes que proporcionan funcionalidades adicionales para construir flujos más complejos y personalizados.

### Componentes Disponibles

* [Custom JS Function](custom-js-function.md) - Ejecuta código JavaScript personalizado
* [Set/Get Variable](set-get-variable.md) - Gestiona variables en el flujo
* [If Else](if-else.md) - Implementa lógica condicional
* [Sticky Note](sticky-note.md) - Agrega notas y documentación al flujo
