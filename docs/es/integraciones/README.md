---
description: Aprende sobre todas las integraciones / nodes disponibles en Flowise
---

# Integrations

***

En Flowise, los nodes son referidos como integraciones. Similar a LEGO, puedes construir un flujo personalizado de orquestación LLM, un chatbot, o un agente con todas las integraciones disponibles en Flowise.

### LangChain

* [Agents](langchain/agents/)
* [Cache](langchain/cache/)
* [Chains](langchain/chains/)
* [Chat Models](langchain/chat-models/)
* [Document Loaders](langchain/document-loaders/)
* [Embeddings](langchain/embeddings/)
* [LLMs](langchain/llms/)
* [Memory](langchain/memory/)
* [Moderation](langchain/moderation/)
* [Output Parsers](langchain/output-parsers/)
* [Prompts](langchain/prompts/)
* [Record Managers](langchain/record-managers.md)
* [Retrievers](langchain/retrievers/)
* [Text Splitters](langchain/text-splitters/)
* [Tools](langchain/tools/)
* [Vector Stores](langchain/vector-stores/)

### LlamaIndex

* [Agents](llamaindex/agents/)
* [Chat Models](llamaindex/chat-models/)
* [Embeddings](llamaindex/embeddings/)
* [Engine](llamaindex/engine/)
* [Response Synthesizer](llamaindex/response-synthesizer/)
* [Tools](llamaindex/tools/)
* [Vector Stores](llamaindex/vector-stores/)

### Utilities

* [Custom JS Function](utilities/custom-js-function.md)
* [Set/Get Variable](utilities/set-get-variable.md)
* [If Else](utilities/if-else.md)
* [Sticky Note](utilities/sticky-note.md)

### External Integrations

* [Zapier Zaps](3rd-party-platform-integration/zapier-zaps.md)
