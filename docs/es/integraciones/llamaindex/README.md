---
description: Aprende sobre las integraciones disponibles de LlamaIndex en Flowise
---

# LlamaIndex

LlamaIndex es un framework de datos para aplicaciones LLM que proporciona las siguientes funcionalidades:
- Estructuración de datos de fuentes externas en representaciones intermedias
- Conexión de datos con LLMs usando diferentes tipos de índices
- Motores de consulta que permiten diferentes patrones de interacción con los datos

### Componentes Disponibles

* [Agents](agents/) - Agentes que pueden interactuar con datos estructurados
* [Chat Models](chat-models/) - Modelos de lenguaje optimizados para chat
* [Embeddings](embeddings/) - Conversión de texto a vectores numéricos
* [Engine](engine/) - Motores para diferentes tipos de consultas e interacciones
* [Response Synthesizer](response-synthesizer/) - Generación de respuestas estructuradas
* [Tools](tools/) - Herramientas para interactuar con datos
* [Vector Stores](vector-stores/) - Almacenamiento y búsqueda de vectores
