---
description: Nodes de Agentes de LangChain
---

# Agents

***

Por sí mismos, los modelos de lenguaje no pueden realizar acciones - solo generan texto.

Los Agents son sistemas que utilizan un LLM como motor de razonamiento para determinar qué acciones tomar y cuáles deberían ser las entradas para esas acciones. Los resultados de esas acciones pueden ser retroalimentados al agente para que determine si se necesitan más acciones o si es apropiado finalizar.

### Nodes de Agentes:

* [Airtable Agent](airtable-agent.md)
* [AutoGPT](autogpt.md)
* [BabyAGI](babyagi.md)
* [CSV Agent](csv-agent.md)
* [Conversational Agent](conversational-agent.md)
* [Conversational Retrieval Agent](conversational-retrieval-agent.md)
* [MistralAI Tool Agent](mistralai-tool-agent.md)
* [OpenAI Assistant](openai-assistant/)
* [OpenAI Function Agent](openai-function-agent.md)
* [OpenAI Tool Agent](../../llamaindex/agents/openai-tool-agent.md)
* [ReAct Agent Chat](react-agent-chat.md)
* [ReAct Agent LLM](react-agent-llm.md)
* [Tool Agent](tool-agent.md)
* [XML Agent](xml-agent.md)
