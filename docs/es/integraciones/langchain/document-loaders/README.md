---
description: Nodos de Cargadores de Documentos LangChain
---

# Cargadores de Documentos

***

Los cargadores de documentos te permiten cargar documentos desde diferentes fuentes como PDF, TXT, CSV, Notion, Confluence, etc. A menudo se utilizan junto con [Vector Stores](../vector-stores/) para ser insertados como embeddings, los cuales pueden ser recuperados posteriormente mediante consultas.

### Mira una introducción sobre los Cargadores de Documentos

{% embed url="https://youtu.be/kMtf9sNIcao" %}

### Nodos de Cargadores de Documentos:

* [Cargador de API](api-loader.md)
* [Airtable](airtable.md)
* [Rastreador de Contenido Web Apify](apify-website-content-crawler.md)
* [Web Scraper Cheerio](cheerio-web-scraper.md)
* [Confluence](confluence.md)
* [Archivo CSV](csv-file.md)
* [Cargador de Documentos Personalizado](custom-document-loader.md)
* [Almacén de Documentos](document-store.md)
* [Archivo Docx](docx-file.md)
* [Figma](figma.md)
* [FireCrawl](firecrawl.md)
* [Carpeta con Archivos](folder-with-files.md)
* [GitBook](gitbook.md)
* [Github](github.md)
* [Archivo JSON](json-file.md)
* [Archivo JSON Lines](json-lines-file.md)
* [Base de Datos Notion](notion-database.md)
* [Carpeta Notion](notion-folder.md)
* [Página Notion](notion-page.md)
* [Archivos PDF](pdf-file.md)
* [Texto Plano](plain-text.md)
* [Web Scraper Playwright](playwright-web-scraper.md)
* [Web Scraper Puppeteer](puppeteer-web-scraper.md)
* [Cargador de Archivos S3](s3-file-loader.md)
* [SearchApi Para Búsqueda Web](searchapi-for-web-search.md)
* Spider
* [SerpApi Para Búsqueda Web](serpapi-for-web-search.md)
* [Spider - búsqueda web y rastreador](spider-web-scraper-crawler.md)
* [Archivo de Texto](text-file.md)
* [Cargador de Archivos No Estructurados](unstructured-file-loader.md)
* [Cargador de Carpetas No Estructuradas](unstructured-folder-loader.md)
* [VectorStore a Documento](vectorstore-to-document.md)
