# Web Scraper Cheerio

Cheerio es ligero y no requiere un entorno de navegador completo como otras herramientas de scraping. Ten en cuenta que al hacer scraping de sitios web, **siempre debes revisar y cumplir con los términos de servicio y políticas del sitio web para asegurar un uso ético y legal de los datos**.

## Extraer de Una URL

1. _(Opcional)_ Conecta [**Text Splitter**](../text-splitters/).
2. Ingresa la URL deseada para hacer scraping.

## Rastrear y Extraer Múltiples URLs

Visita la guía de [**Web Crawl**](../../../use-cases/web-scrape-qna.md#id-1.-crawl-multiple-pages) para permitir el scraping de múltiples páginas.

## Salida

Carga el contenido de la URL como Documento

## Recursos

* [LangChain JS Cheerio](https://js.langchain.com/docs/integrations/document_loaders/web_loaders/web_cheerio)
* [Cheerio](https://cheerio.js.org/)
