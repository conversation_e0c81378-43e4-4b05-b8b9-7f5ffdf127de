---
description: Haz scraping y crawling de la web con Spider - el web scraper y crawler de código abierto más rápido.
---

# Spider Web Scraper/Crawler

<figure><img src="../../../.gitbook/assets/spider.png" alt="Nodo Spider" width="365"><figcaption><p>Nodo Spider Web Scraper/Crawler</p></figcaption></figure>

[Spider](https://spider.cloud/?ref=flowise) es el web scraper y crawler de código abierto más rápido que devuelve datos preparados para LLM. Para comenzar a usar este nodo necesitas una clave API de [Spider.cloud](https://spider.cloud/?ref=flowise).

## Comenzar

1. Ve al sitio web de [Spider.cloud](https://spider.cloud/?ref=flowise) y regístrate para obtener una cuenta gratuita.
2. Luego ve a [API Keys](https://spider.cloud/api-keys) y crea una nueva clave API.
3. Copia la clave API y pégala en el campo "Credential" en el nodo Spider.

## Scraping y Crawling

1. Elige "Scrape" o "Crawl" en el menú desplegable de modo.
2. Ingresa la URL que deseas analizar o rastrear en el campo "Web Page URL".
3. Si elegiste "Crawl", ingresa la cantidad máxima de páginas que deseas rastrear en el campo "Limit". Si no se ingresa ningún valor o se ingresa 0, el crawler rastreará todas las páginas.

## Ejemplo

<figure><img src="../../../.gitbook/assets/spider_example_usage.png" alt="Ejemplo de uso del nodo spider" width="365"><figcaption><p>Ejemplo de uso del nodo Spider</p></figcaption></figure>
