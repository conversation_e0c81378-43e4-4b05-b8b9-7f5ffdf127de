# Playwright Web Scraper

Playwright es una biblioteca de Node.js que permite la automatización de navegadores web para web scraping. Fue desarrollada por Microsoft y soporta múltiples navegadores, incluyendo Chromium. Ten en cuenta que al hacer scraping de sitios web, **siempre debes revisar y cumplir con los términos de servicio y políticas del sitio web para asegurar un uso ético y legal de los datos**.

## Scraping de Una URL

1.  _(Opcional)_ Conecta **[Text Splitter](../text-splitters/)**.
2. Ingresa la URL deseada para hacer scraping.

## Crawl y Scraping de Múltiples URLs
Visita la guía de **[Web Crawl](../../use-cases/web-crawl.md)** para permitir el scraping de múltiples páginas.

## Salida

Carga el contenido de la URL como Documento

## Recursos

* [<PERSON><PERSON><PERSON><PERSON> JS Playwright](https://js.langchain.com/docs/integrations/document_loaders/web_loaders/web_playwright)
* [Playwright](https://playwright.dev/)
