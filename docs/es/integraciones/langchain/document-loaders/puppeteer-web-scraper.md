# Puppeteer Web Scraper

Puppeteer es una biblioteca de Node.js que controla Chrome/Chromium a través del DevTools Protocol en modo headless. Ten en cuenta que al hacer scraping de sitios web, **siempre debes revisar y cumplir con los términos de servicio y políticas del sitio web para asegurar un uso ético y legal de los datos**.

## Scraping de Una URL

1.  _(Opcional)_ Conecta **[Text Splitter](../text-splitters/)**.
2. Ingresa la URL deseada para hacer scraping.

## Crawl y Scraping de Múltiples URLs
Visita la guía de **[Web Crawl](../../use-cases/web-crawl.md)** para permitir el scraping de múltiples páginas.

## Salida

Carga el contenido de la URL como Documento

## Recursos

* [LangChain JS Puppeteer](https://js.langchain.com/docs/integrations/document_loaders/web_loaders/web_puppeteer)
* [Puppeteer](https://pptr.dev/)
