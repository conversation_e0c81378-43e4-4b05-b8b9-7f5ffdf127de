# Code Interpreter by E2B

[E2B](https://e2b.dev/) es un runtime de código abierto para ejecutar código generado por IA en sandboxes seguros en la nube. Por ejemplo, cuando el usuario solicita generar un gráfico de barras de los datos, el LLM generará el código Python necesario para graficar. Este código generado se enviará a E2B, y la salida de la ejecución contendrá imágenes del gráfico, códigos, texto, etc. Estas salidas se envían de vuelta al LLM para el procesamiento final antes de mostrarse en el chat.

<figure><img src="../../../.gitbook/assets/image (176).png" alt=""><figcaption></figcaption></figure>
