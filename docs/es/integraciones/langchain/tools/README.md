---
description: Nodos de Herramientas LangChain
---

# Tools

***

Las Tools (herramientas) son funciones que los agents pueden utilizar para interactuar con el mundo. Estas herramientas pueden ser utilidades genéricas (por ejemplo, búsqueda), otras chains, o incluso otros agents.

### Nodos de Tools:

* [BraveSearch API](bravesearch-api.md)
* [Calculator](calculator.md)
* [Chain Tool](chain-tool.md)
* [Chatflow Tool](chatflow-tool.md)
* [Custom Tool](custom-tool.md)
* [Exa Search](exa-search.md)
* [Google Custom Search](google-custom-search.md)
* [OpenAPI Toolkit](openapi-toolkit.md)
* [Python Interpreter](python-interpreter.md)
* [Read File](read-file.md)
* [Request Get](request-get.md)
* [Request Post](request-post.md)
* [Retriever Tool](retriever-tool.md)
* [SearchApi](searchapi.md)
* [SearXNG](searxng.md)
* [Serp API](serp-api.md)
* [Serper](serper.md)
* [Web Browser](web-browser.md)
* [Write File](write-file.md)
