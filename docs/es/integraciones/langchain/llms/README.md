---
description: Nodos LLM de LangChain
---

# LLMs

***

Un modelo de lenguaje grande, abreviado como LLM, es un sistema de AI entrenado en cantidades masivas de datos de texto. Esto les permite comunicarse y generar texto similar al humano en respuesta a una amplia gama de indicaciones y preguntas. En esencia, pueden entender y responder al lenguaje complejo.

### Nodos LLM:

* [AWS Bedrock](aws-bedrock.md)
* [Azure OpenAI](azure-openai.md)
* [NIBittensorLLM](broken-reference)
* [Cohere](cohere.md)
* [GooglePaLM](broken-reference)
* [GoogleVertex AI](googlevertex-ai.md)
* [HuggingFace Inference](huggingface-inference.md)
* [Ollama](ollama.md)
* [OpenAI](openai.md)
* [Replicate](replicate.md)
