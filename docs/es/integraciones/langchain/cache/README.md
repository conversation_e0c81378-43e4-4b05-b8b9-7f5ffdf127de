---
description: Nodes de Cache de LangChain
---

# Cache

***

El caching puede ahorrarte dinero reduciendo el número de llamadas a la API que haces al proveedor de LLM, si frecuentemente solicitas la misma completion múltiples veces. Puede acelerar tu aplicación reduciendo el número de llamadas a la API que haces al proveedor de LLM.

### Nodes de Cache:

* [InMemory Cache](in-memory-cache.md)
* [InMemory Embedding Cache](inmemory-embedding-cache.md)
* [Momento Cache](momento-cache.md)
* [Redis Cache](redis-cache.md)
* [Redis Embeddings Cache](redis-embeddings-cache.md)
* [Upstash Redis Cache](upstash-redis-cache.md)
