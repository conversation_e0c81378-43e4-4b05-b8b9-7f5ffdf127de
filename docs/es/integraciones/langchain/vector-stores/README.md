---
description: Nodos de Vector Store de LangChain
---

# Vector Stores

***

Un vector store o base de datos vectorial se refiere a un tipo de sistema de base de datos especializado en almacenar y recuperar vectores numéricos de alta dimensión. Los vector stores están diseñados para gestionar e indexar estos vectores de manera eficiente, permitiendo búsquedas de similitud rápidas.

### Mira una introducción sobre Vector Stores y cómo puedes usarlos en Flowise

{% embed url="https://youtu.be/m0nr1_pnAxc" %}

### Nodos de Vector Store:

* [AstraDB](astradb.md)
* [Chroma](chroma.md)
* [Elastic](elastic.md)
* [Faiss](faiss.md)
* [In-Memory Vector Store](in-memory-vector-store.md)
* [Milvus](milvus.md)
* [MongoDB Atlas](mongodb-atlas.md)
* [OpenSearch](opensearch.md)
* [Pinecone](pinecone.md)
* [Postgres](postgres.md)
* [Qdrant](qdrant.md)
* [Redis](redis.md)
* [SingleStore](singlestore.md)
* [Supabase](supabase.md)
* [Upstash Vector](upstash-vector.md)
* [Vectara](vectara.md)
* [Weaviate](weaviate.md)
* [Zep Collection - Open Source](zep-collection-open-source.md)
* [Zep Collection - Cloud](zep-collection-cloud.md)
