---
description: Nodes de Modelos de Chat de LangChain
---

# Chat Models

***

Los modelos de chat toman una lista de mensajes como entrada y devuelven un mensaje generado por el modelo como salida. Estos modelos como **gpt-3.5-turbo** o **gpt4** son más potentes y más económicos que sus predecesores, los modelos de Completions como **text-davincii-003**.

### Nodes de Modelos de Chat:

* [AWS ChatBedrock](aws-chatbedrock.md)
* [Azure ChatOpenAI](../../llamaindex/chat-models/azurechatopenai.md)
* [NIBittensorChat](broken-reference)
* [ChatAnthropic](chatanthropic.md)
* [ChatCohere](chatcohere.md)
* [Chat Fireworks](chat-fireworks.md)
* [ChatGoogleGenerativeAI](google-ai.md)
* [ChatGooglePaLM](broken-reference)
* [Google VertexAI](google-vertexai.md)
* [ChatHuggingFace](chathuggingface.md)
* [ChatLocalAI](chatlocalai.md)
* [ChatMistralAI](mistral-ai.md)
* [ChatOllama](chatollama.md)
* [ChatOllama Funtion](broken-reference)
* [ChatOpenAI](azure-chatopenai.md)
* [ChatOpenAI Custom](broken-reference)
* [ChatTogetherAI](chattogetherai.md)
* [GroqChat](groqchat.md)
