# Nvdia NIM

## Prerequisitos

1. Inicia sesión o regístrate en [Nvdia](https://build.nvidia.com/).
2. Desde la barra de navegación superior, haz clic en NIM:

<figure><img src="../../../.gitbook/assets/image (247).png" alt=""><figcaption></figcaption></figure>

3. Busca el modelo que te gustaría usar. Para descargarlo localmente, usaremos Docker:

<figure><img src="../../../.gitbook/assets/image (248).png" alt=""><figcaption></figcaption></figure>

4. Sigue las instrucciones de la configuración de Docker. Primero debes obtener una API Key para descargar la imagen de Docker:

<figure><img src="../../../.gitbook/assets/image (249).png" alt="" width="563"><figcaption></figcaption></figure>

## Flowise

1. **Chat Models** > arrastra el nodo **Chat NvdiaNIM**

<figure><img src="../../../.gitbook/assets/image (250).png" alt=""><figcaption></figcaption></figure>

2. Si estás usando un endpoint alojado en Nvdia, debes tener tu API key. **Connect Credential** > haz clic en **Create New**. Sin embargo, si estás usando una configuración local, esto es opcional.

<div align="left"><figure><img src="../../../.gitbook/assets/image (251).png" alt=""><figcaption></figcaption></figure> <figure><img src="../../../.gitbook/assets/Screenshot 2024-12-23 180712.png" alt=""><figcaption></figcaption></figure></div>

3. Ingresa el nombre del modelo y ¡voilà [🎉](https://emojipedia.org/party-popper/), tu **nodo Nvdia NIM** está listo para ser usado en Flowise!

<figure><img src="../../../.gitbook/assets/image (252).png" alt=""><figcaption></figcaption></figure>

## Recursos

* [Nvida LLM Getting Started](https://docs.nvidia.com/nim/large-language-models/latest/getting-started.html)
* [Nvdia NIM](https://build.nvidia.com/microsoft/phi-3-mini-4k?snippet_tab=Docker)
