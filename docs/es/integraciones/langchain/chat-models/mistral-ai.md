# ChatMistralAI

## Prerrequisitos

1. Registra una cuenta en [Mistral AI](https://mistral.ai/)
2. Crea una [API key](https://console.mistral.ai/user/api-keys/)

## Configuración

1. **Chat Models** > arrastra el node **ChatMistralAI**

<figure><img src="../../../.gitbook/assets/mistral_ai/1.png" alt="" width="563"><figcaption></figcaption></figure>

2. **Connect Credential** > haz click en **Create New**

<figure><img src="../../../.gitbook/assets/mistral_ai/2.png" alt="" width="278"><figcaption></figcaption></figure>

3. Completa la credencial de **Mistral AI**

<figure><img src="../../../.gitbook/assets/mistral_ai/3.png" alt="" width="563"><figcaption></figcaption></figure>

4. ¡Listo! [🎉](https://emojipedia.org/party-popper/), ahora puedes usar el **node de ChatMistralAI** en Flowise

<figure><img src="../../../.gitbook/assets/mistral_ai/4.png" alt=""><figcaption></figcaption></figure>

## Recursos

* [LangChain JS ChatMistralAI](https://js.langchain.com/docs/integrations/chat/mistral)
* [Mistral AI](https://mistral.ai/)
* [Documentación de Mistral AI](https://docs.mistral.ai/)