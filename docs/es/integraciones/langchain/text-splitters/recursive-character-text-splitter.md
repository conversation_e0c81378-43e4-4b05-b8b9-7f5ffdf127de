---
description: >-
  Divide documentos recursivamente por diferentes caracteres - comenzando con "\n\n",
  luego "\n", y finalmente " ".
---

# Recursive Character Text Splitter

<figure><img src="../../../.gitbook/assets/image (155).png" alt="" width="305"><figcaption><p>Nodo Recursive Character Text Splitter</p></figcaption></figure>

{% hint style="info" %}
Esta sección está en desarrollo. Agradecemos cualquier ayuda que puedas proporcionar para completar esta sección. Por favor, consulta nuestra [Guía de Contribución](../../../contributing/) para comenzar.
{% endhint %}
