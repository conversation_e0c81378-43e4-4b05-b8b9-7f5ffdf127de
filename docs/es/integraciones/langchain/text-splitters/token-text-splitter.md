---
description: >-
  Divide una cadena de texto sin procesar primero convirtiendo el texto en tokens BPE, luego
  divide estos tokens en fragmentos y convierte los tokens dentro de un solo fragmento
  de vuelta a texto.
---

# Token Text Splitter

<figure><img src="../../../.gitbook/assets/image (156).png" alt="" width="305"><figcaption><p>Nodo Token Text Splitter</p></figcaption></figure>

{% hint style="info" %}
Esta sección está en desarrollo. Agradecemos cualquier ayuda que puedas proporcionar para completar esta sección. Por favor, consulta nuestra [Guía de Contribución](../../../contributing/) para comenzar.
{% endhint %}
