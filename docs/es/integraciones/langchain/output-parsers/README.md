---
description: Nodos Output Parser de <PERSON>
---

# Output Parsers

***

Los nodos Output Parser son responsables de tomar la salida de un modelo y transformarla en un formato más adecuado para tareas posteriores. Son útiles cuando estás usando LLMs para generar datos estructurados, o para normalizar la salida de modelos de chat y LLMs.

### Nodos Output Parser:

* [CSV Output Parser](csv-output-parser.md)
* [Custom List Output Parser](custom-list-output-parser.md)
* [Structured Output Parser](structured-output-parser.md)
* [Advanced Structured Output Parser](advanced-structured-output-parser.md)
