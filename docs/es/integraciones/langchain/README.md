---
description: Aprende sobre las integraciones disponibles de LangChain en Flowise
---

# <PERSON><PERSON><PERSON><PERSON> es un framework para desarrollar aplicaciones potenciadas por modelos de lenguaje. Proporciona varios componentes que puedes utilizar para crear aplicaciones de LLM personalizadas.

### Componentes Disponibles

* [Agents](agents/) - Agentes que pueden usar herramientas para completar tareas
* [Cache](cache/) - Almacenamiento en caché para respuestas de LLM y embeddings
* [Chains](chains/) - Secuencias de llamadas combinadas para tareas específicas
* [Chat Models](chat-models/) - Modelos de lenguaje optimizados para chat
* [Document Loaders](document-loaders/) - Cargadores para diferentes tipos de documentos
* [Embeddings](embeddings/) - Conversión de texto a vectores numéricos
* [LLMs](llms/) - Modelos de lenguaje de gran escala
* [Memory](memory/) - Almacenamiento de historial de conversaciones
* [Moderation](moderation/) - Filtrado de contenido inapropiado
* [Output Parsers](output-parsers/) - Parseo de respuestas de LLM a formatos estructurados
* [Prompts](prompts/) - Plantillas para instrucciones a LLMs
* [Record Managers](record-managers.md) - Gestión de registros y datos
* [Retrievers](retrievers/) - Recuperación de documentos relevantes
* [Text Splitters](text-splitters/) - División de texto en segmentos manejables
* [Tools](tools/) - Herramientas que los agentes pueden utilizar
* [Vector Stores](vector-stores/) - Almacenamiento y búsqueda de vectores
