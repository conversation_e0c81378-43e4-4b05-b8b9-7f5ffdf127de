---
description: Nodos Retriever de Lang<PERSON>hain
---

# Retrievers

***

Los nodos Retriever devuelven documentos dado una consulta no estructurada. Es más general que un vector store. Un retriever no necesita ser capaz de almacenar documentos, solo de devolverlos (o recuperarlos).

### Nodos Retriever:

* [Cohere Rerank Retriever](cohere-rerank-retriever.md)
* [Embeddings Filter Retriever](embeddings-filter-retriever.md)
* [HyDE Retriever](hyde-retriever.md)
* [LLM Filter Retriever](llm-filter-retriever.md)
* [Multi Query Retriever](multi-query-retriever.md)
* [Prompt Retriever](prompt-retriever.md)
* [Reciprocal Rank Fusion Retriever](reciprocal-rank-fusion-retriever.md)
* [Similarity Score Threshold Retriever](similarity-score-threshold-retriever.md)
* [Vector Store Retriever](vector-store-retriever.md)
* [Voyage AI Rerank Retriever](page.md)
