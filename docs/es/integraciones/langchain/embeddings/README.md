---
description: Nodos de Embedding de LangChain
---

# Embeddings

***

Un embedding es un vector (lista) de números de punto flotante. La distancia entre dos vectores mide su relación. Distancias pequeñas sugieren una alta relación y distancias grandes sugieren una baja relación.

Los embeddings pueden usarse para crear una representación numérica de datos textuales. Esta representación numérica es útil porque puede usarse para encontrar documentos similares.

Se utilizan comúnmente para:

* Búsqueda (donde los resultados se clasifican por relevancia a una consulta)
* Agrupación (donde las cadenas de texto se agrupan por similitud)
* Recomendaciones (donde se recomiendan elementos con cadenas de texto relacionadas)
* Detección de anomalías (donde se identifican valores atípicos con poca relación)
* Medición de diversidad (donde se analizan las distribuciones de similitud)
* Clasificación (donde las cadenas de texto se clasifican por su etiqueta más similar)

### Nodos de Embedding:

* [AWS Bedrock Embeddings](aws-bedrock-embeddings.md)
* [Azure OpenAI Embeddings](azure-openai-embeddings.md)
* [Cohere Embeddings](cohere-embeddings.md)
* [Google GenerativeAI Embeddings](googlegenerativeai-embeddings.md)
* [Google PaLM Embeddings](broken-reference)
* [Google VertexAI Embeddings](googlevertexai-embeddings.md)
* [HuggingFace Inference Embeddings](huggingface-inference-embeddings.md)
* [LocalAI Embeddings](localai-embeddings.md)
* [MistralAI Embeddings](mistralai-embeddings.md)
* [Ollama Embeddings](ollama-embeddings.md)
* [OpenAI Embeddings](openai-embeddings.md)
* [OpenAI Embeddings Custom](openai-embeddings-custom.md)
* [TogetherAI Embedding](togetherai-embedding.md)
* [VoyageAI Embeddings](voyageai-embeddings.md)
