# Azure OpenAI Embeddings

## Prerequisito

1. [Inici<PERSON> sesión](https://portal.azure.com/) o [regístrate](https://azure.microsoft.com/en-us/free/) en Azure
2. [Crea](https://portal.azure.com/#create/Microsoft.CognitiveServicesOpenAI) tu Azure OpenAI y espera la aprobación aproximadamente 10 días hábiles
3. Tu API key estará disponible en **Azure OpenAI** > haz clic en **name_azure_openai** > haz clic en **Click here to manage keys**

<figure><img src="../../../.gitbook/assets/azure/azure-general/1.png" alt=""><figcaption></figcaption></figure>

## Configuración

### Azure OpenAI Embeddings

1. Haz clic en **Go to Azure OpenaAI Studio**

<figure><img src="../../../.gitbook/assets/azure/azure-general/2.png" alt=""><figcaption></figcaption></figure>

2. Haz clic en **Deployments**

<figure><img src="../../../.gitbook/assets/azure/azure-general/3.png" alt=""><figcaption></figcaption></figure>

3. Haz clic en **Create new deployment**

<figure><img src="../../../.gitbook/assets/azure/azure-general/4.png" alt=""><figcaption></figcaption></figure>

4. Selecciona como se muestra a continuación y haz clic en **Create**

<figure><img src="../../../.gitbook/assets/azure/azure-openai-embeddings/1.png" alt="" width="559"><figcaption></figcaption></figure>

5. Azure OpenAI Embeddings creado exitosamente

* Deployment name: `text-embedding-ada-002`
* Instance name: `esquina superior derecha`

<figure><img src="../../../.gitbook/assets/azure/azure-openai-embeddings/2.png" alt=""><figcaption></figcaption></figure>

### Flowise

1. **Embeddings** > arrastra el nodo **Azure OpenAI Embeddings**

<figure><img src="../../../.gitbook/assets/azure/azure-openai-embeddings/3.png" alt="" width="563"><figcaption></figcaption></figure>

2. **Connect Credential** > haz clic en **Create New**

<figure><img src="../../../.gitbook/assets/azure/azure-openai-embeddings/4.png" alt="" width="386"><figcaption></figcaption></figure>

3. Copia y pega cada detalle (API Key, Instance y Deployment name, [API Version](https://learn.microsoft.com/en-us/azure/ai-services/openai/reference#chat-completions)) en la credencial de **Azure OpenAI Embeddings**

<figure><img src="../../../.gitbook/assets/azure/azure-openai-embeddings/5.png" alt="" width="554"><figcaption></figcaption></figure>

4. ¡Voilà [🎉](https://emojipedia.org/party-popper/), has creado el **nodo Azure OpenAI Embeddings** en Flowise

<figure><img src="../../../.gitbook/assets/azure/azure-general/5.png" alt=""><figcaption></figcaption></figure>

## Recursos

* [LangChain JS Azure OpenAI Embeddings](https://js.langchain.com/docs/modules/data_connection/text_embedding/integrations/azure_openai)
* [Referencia de la API REST de Azure OpenAI Service](https://learn.microsoft.com/en-us/azure/ai-services/openai/reference)
