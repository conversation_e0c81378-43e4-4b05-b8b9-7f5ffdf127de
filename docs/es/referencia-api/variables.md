# Variables

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/variables" method="post" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/variables" method="get" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/variables/{id}" method="put" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/variables/{id}" method="delete" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}
