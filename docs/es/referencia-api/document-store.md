# Document Store

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/document-store/store" method="post" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/document-store/store" method="get" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/document-store/store/{id}" method="get" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/document-store/store/{id}" method="put" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/document-store/store/{id}" method="delete" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (3).yml" path="/document-store/upsert/{id}" method="post" %}
[swagger (3).yml](<../.gitbook/assets/swagger (3).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (3).yml" path="/document-store/refresh/{id}" method="post" %}
[swagger (3).yml](<../.gitbook/assets/swagger (3).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/document-store/vectorstore/query" method="post" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger.yml" path="/document-store/loader/{storeId}/{loaderId}" method="delete" %}
[swagger.yml](../.gitbook/assets/swagger.yml)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/document-store/vectorstore/{id}" method="delete" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger.yml" path="/document-store/chunks/{storeId}/{loaderId}/{pageNo}" method="get" %}
[swagger.yml](../.gitbook/assets/swagger.yml)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger.yml" path="/document-store/chunks/{storeId}/{loaderId}/{chunkId}" method="put" %}
[swagger.yml](../.gitbook/assets/swagger.yml)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger.yml" path="/document-store/chunks/{storeId}/{loaderId}/{chunkId}" method="delete" %}
[swagger.yml](../.gitbook/assets/swagger.yml)
{% endswagger %}

