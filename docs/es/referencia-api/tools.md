# Tools

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/tools" method="post" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/tools" method="get" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/tools/{id}" method="get" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/tools/{id}" method="put" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/tools/{id}" method="delete" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}
