# Assistants

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/assistants" method="post" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1).yml" path="/assistants" method="get" %}
[swagger (1) (1).yml](<../.gitbook/assets/swagger (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1).yml" path="/assistants/{id}" method="get" %}
[swagger (1) (1).yml](<../.gitbook/assets/swagger (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1).yml" path="/assistants/{id}" method="put" %}
[swagger (1) (1).yml](<../.gitbook/assets/swagger (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1).yml" path="/assistants/{id}" method="delete" %}
[swagger (1) (1).yml](<../.gitbook/assets/swagger (1) (1).yml>)
{% endswagger %}
