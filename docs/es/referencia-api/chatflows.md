# Chatflows

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/chatflows" method="get" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/chatflows" method="post" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/chatflows/{id}" method="get" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/chatflows/{id}" method="put" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/chatflows/{id}" method="delete" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}

{% swagger src="../.gitbook/assets/swagger (1) (1) (1).yml" path="/chatflows/apikey/{apikey}" method="get" %}
[swagger (1) (1) (1).yml](<../.gitbook/assets/swagger (1) (1) (1).yml>)
{% endswagger %}
