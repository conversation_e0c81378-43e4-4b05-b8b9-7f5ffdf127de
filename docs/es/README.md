---
description: Bienvenido a la documentación de Universo Platformo basada en Flowise
---

# Introducción

> **📋 Aviso**: Esta documentación está basada en la documentación original de Flowise y actualmente se está adaptando para Universo Platformo React. Algunas secciones pueden aún hacer referencia a funcionalidades de Flowise que no han sido completamente actualizadas para las características específicas de Universo Platformo.

<figure><img src=".gitbook/assets/FlowiseIntro (1).gif" alt=""><figcaption></figcaption></figure>

**Universo Platformo React** es una plataforma avanzada de código abierto construida sobre Flowise, extendiéndola con capacidades poderosas para crear Agentes de IA, flujos de trabajo LLM y experiencias inmersivas 3D/AR/VR.

Ofrece una solución completa que incluye:

* [x] Constructor Visual (heredado de Flowise)
* [x] Trazabilidad y análisis
* [x] Evaluaciones
* [x] Intervención humana (Human in the Loop)
* [x] API, CLI, SDK, chatbot embebido
* [x] Equipos y espacios de trabajo
* [x] **Sistema de Nodos UPDL** - Lenguaje Universal de Descripción de Plataformas
* [x] **Plantillas MMOOMM** - Metaversos Abiertos Multijugador Masivos en Línea
* [x] **Exportación Multiplataforma** - Soporte para AR.js, PlayCanvas, A-Frame
* [x] **Sistemas de Recursos Mejorados** - Gestión avanzada de recursos

Existen tres constructores visuales principales:

* Assistant
* Chatflow
* Agentflow

## Assistant

Assistant es la forma más amigable para principiantes de crear un Agente de IA. Los usuarios pueden crear un asistente conversacional capaz de seguir instrucciones, utilizar herramientas cuando sea necesario y recuperar información desde archivos subidos (RAG) para responder a consultas del usuario.

<figure><picture><source srcset=".gitbook/assets/Screenshot 2025-05-12 215934.png" media="(prefers-color-scheme: dark)"><img src=".gitbook/assets/image.png" alt=""></picture><figcaption></figcaption></figure>

## Chatflow

Chatflow está diseñado para construir sistemas de agente único, chatbots y flujos simples con modelos de lenguaje (LLM). Es más flexible que Assistant. Los usuarios pueden utilizar técnicas avanzadas como Graph RAG, Reranker, código personalizado, entre otros.

<figure><picture><source srcset=".gitbook/assets/dark.png" media="(prefers-color-scheme: dark)"><img src=".gitbook/assets/white.png" alt=""></picture><figcaption></figcaption></figure>

## Agentflow

Agentflow es el superconjunto de Chatflow y Assistant. Se puede utilizar para crear asistentes conversacionales, sistemas de agente único, sistemas multi-agente y orquestaciones de flujo de trabajo complejas.

<figure><img src=".gitbook/assets/FlowiseIntro.gif" alt=""><figcaption></figcaption></figure>

### Ve Flowise en acción

En esta demo de inicio rápido de 2 minutos, aprenderás los fundamentos básicos de cómo funciona Flowise.

{% embed url="https://youtu.be/d7vfUodP0c4" %}
Construyendo una aplicación de preguntas y respuestas con PDF
{% endembed %}

### Construyendo tu primera aplicación LLM

Este video de 10 minutos te enseñará cómo construir tu primera aplicación LLM usando Flowise.

{% embed url="https://youtu.be/kAyKOsm8L5Y" %}
Construye tu primera aplicación LLM desde cero con Flowise
{% endembed %}

## Contribuir

Si quieres ayudar a este proyecto, por favor considera revisar la [Guía de Contribución](contributing/).

## ¿Necesitas Ayuda?

Para soporte y más discusiones, únete a nuestro servidor de [Discord](https://discord.gg/jbaHfsRVBW).
