---
description: Aprende a construir tus propias soluciones Flowise a través de ejemplos prácticos
---

# Use Cases

***

Esta sección proporciona una colección de ejemplos prácticos para demostrar cómo se puede usar Flowise para construir una variedad de soluciones.

Cada use case te guiará a través del proceso de diseñar, construir e implementar aplicaciones del mundo real usando Flowise.

## Guías

* [Llamando a Children Flows](calling-children-flows.md)
* [Llamando a Webhook](webhook-tool.md)
* [Interactuando con API](interacting-with-api.md)
* [QnA con Múltiples Documentos](multiple-documents-qna.md)
* [QnA con SQL](sql-qna.md)
* [Upserting de Datos](upserting-data.md)
* [QnA con Web Scraping](web-scrape-qna.md)
