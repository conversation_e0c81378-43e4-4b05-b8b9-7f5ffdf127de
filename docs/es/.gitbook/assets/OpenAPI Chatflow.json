{"nodes": [{"id": "openAIToolAgent_0", "position": {"x": 2043.452556953855, "y": 419.243930407276}, "type": "customNode", "data": {"id": "openAIToolAgent_0", "label": "OpenAI Tool Agent", "version": 1, "name": "openAIToolAgent", "type": "AgentExecutor", "baseClasses": ["AgentExecutor", "BaseChain", "Runnable"], "category": "Agents", "description": "Agent that uses OpenAI Function Calling to pick the tools and args to call", "inputParams": [{"label": "System Message", "name": "systemMessage", "type": "string", "rows": 4, "optional": true, "additionalParams": true, "id": "openAIToolAgent_0-input-systemMessage-string"}], "inputAnchors": [{"label": "Tools", "name": "tools", "type": "Tool", "list": true, "id": "openAIToolAgent_0-input-tools-Tool"}, {"label": "Memory", "name": "memory", "type": "BaseChatMemory", "id": "openAIToolAgent_0-input-memory-BaseChatMemory"}, {"label": "OpenAI/Azure Chat Model", "name": "model", "type": "BaseChatModel", "id": "openAIToolAgent_0-input-model-BaseChatModel"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "openAIToolAgent_0-input-inputModeration-Moderation"}], "inputs": {"tools": ["{{chainTool_0.data.instance}}"], "memory": "{{bufferMemory_0.data.instance}}", "model": "{{chatOpenAI_1.data.instance}}", "systemMessage": "", "inputModeration": ""}, "outputAnchors": [{"id": "openAIToolAgent_0-output-openAIToolAgent-AgentExecutor|BaseChain|Runnable", "name": "openAIToolAgent", "label": "AgentExecutor", "description": "Agent that uses OpenAI Function Calling to pick the tools and args to call", "type": "AgentExecutor | BaseChain | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 434, "selected": false, "positionAbsolute": {"x": 2043.452556953855, "y": 419.243930407276}, "dragging": false}, {"id": "chatOpenAI_1", "position": {"x": 1172.9727392624852, "y": 531.4096673286917}, "type": "customNode", "data": {"id": "chatOpenAI_1", "label": "ChatOpenAI", "version": 5, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_1-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "options", "options": [{"label": "gpt-4", "name": "gpt-4"}, {"label": "gpt-4-turbo-preview", "name": "gpt-4-turbo-preview"}, {"label": "gpt-4-0125-preview", "name": "gpt-4-0125-preview"}, {"label": "gpt-4-1106-preview", "name": "gpt-4-1106-preview"}, {"label": "gpt-4-1106-vision-preview", "name": "gpt-4-1106-vision-preview"}, {"label": "gpt-4-vision-preview", "name": "gpt-4-vision-preview"}, {"label": "gpt-4-0613", "name": "gpt-4-0613"}, {"label": "gpt-4-32k", "name": "gpt-4-32k"}, {"label": "gpt-4-32k-0613", "name": "gpt-4-32k-0613"}, {"label": "gpt-3.5-turbo", "name": "gpt-3.5-turbo"}, {"label": "gpt-3.5-turbo-0125", "name": "gpt-3.5-turbo-0125"}, {"label": "gpt-3.5-turbo-1106", "name": "gpt-3.5-turbo-1106"}, {"label": "gpt-3.5-turbo-0613", "name": "gpt-3.5-turbo-0613"}, {"label": "gpt-3.5-turbo-16k", "name": "gpt-3.5-turbo-16k"}, {"label": "gpt-3.5-turbo-16k-0613", "name": "gpt-3.5-turbo-16k-0613"}], "default": "gpt-3.5-turbo", "optional": true, "id": "chatOpenAI_1-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_1-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_1-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_1-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_1-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-3.5-turbo-16k", "temperature": "0", "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": "", "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_1-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 669, "selected": false, "positionAbsolute": {"x": 1172.9727392624852, "y": 531.4096673286917}, "dragging": false}, {"id": "bufferMemory_0", "position": {"x": 748.796752834334, "y": 770.3068397228885}, "type": "customNode", "data": {"id": "bufferMemory_0", "label": "Buffer Memory", "version": 1, "name": "bufferMemory", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BaseChatMemory", "BaseMemory"], "category": "Memory", "description": "Remembers previous conversational back and forths directly", "inputParams": [{"label": "Memory Key", "name": "<PERSON><PERSON><PERSON>", "type": "string", "default": "chat_history", "id": "bufferMemory_0-input-memoryKey-string"}, {"label": "Input Key", "name": "inputKey", "type": "string", "default": "input", "id": "bufferMemory_0-input-inputKey-string"}], "inputAnchors": [], "inputs": {"memoryKey": "chat_history", "inputKey": "input"}, "outputAnchors": [{"id": "bufferMemory_0-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory", "name": "bufferMemory", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Remembers previous conversational back and forths directly", "type": "BufferMemory | BaseChatMemory | BaseMemory"}], "outputs": {}, "selected": false}, "width": 300, "height": 376, "selected": false, "positionAbsolute": {"x": 748.796752834334, "y": 770.3068397228885}, "dragging": false}, {"id": "openApiChain_0", "position": {"x": 1145.3060530569107, "y": -103.11418698407195}, "type": "customNode", "data": {"id": "openApiChain_0", "label": "OpenAPI Chain", "version": 2, "name": "openApiChain", "type": "OpenAPIChain", "baseClasses": ["OpenAPIChain", "BaseChain", "Runnable"], "category": "Chains", "description": "Chain that automatically select and call APIs based only on an OpenAPI spec", "inputParams": [{"label": "YAML Link", "name": "yamlLink", "type": "string", "placeholder": "https://api.speak.com/openapi.yaml", "description": "If YAML link is provided, uploaded YAML File will be ignored and YAML link will be used instead", "id": "openApiChain_0-input-yamlLink-string"}, {"label": "YAML File", "name": "yamlFile", "type": "file", "fileType": ".yaml", "description": "If YAML link is provided, uploaded YAML File will be ignored and YAML link will be used instead", "id": "openApiChain_0-input-yamlFile-file"}, {"label": "Headers", "name": "headers", "type": "json", "additionalParams": true, "optional": true, "id": "openApiChain_0-input-headers-json"}], "inputAnchors": [{"label": "ChatOpenAI Model", "name": "model", "type": "ChatOpenAI", "id": "openApiChain_0-input-model-ChatOpenAI"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "openApiChain_0-input-inputModeration-Moderation"}], "inputs": {"model": "{{chatOpenAI_2.data.instance}}", "yamlLink": "", "headers": "", "inputModeration": ""}, "outputAnchors": [{"id": "openApiChain_0-output-openApiChain-OpenAPIChain|BaseChain|Runnable", "name": "openApiChain", "label": "OpenAPIChain", "description": "Chain that automatically select and call APIs based only on an OpenAPI spec", "type": "OpenAPIChain | BaseChain | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 561, "selected": false, "positionAbsolute": {"x": 1145.3060530569107, "y": -103.11418698407195}, "dragging": false}, {"id": "chatOpenAI_2", "position": {"x": 759.2922754505641, "y": -147.3984427366269}, "type": "customNode", "data": {"id": "chatOpenAI_2", "label": "ChatOpenAI", "version": 5, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_2-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "options", "options": [{"label": "gpt-4", "name": "gpt-4"}, {"label": "gpt-4-turbo-preview", "name": "gpt-4-turbo-preview"}, {"label": "gpt-4-0125-preview", "name": "gpt-4-0125-preview"}, {"label": "gpt-4-1106-preview", "name": "gpt-4-1106-preview"}, {"label": "gpt-4-1106-vision-preview", "name": "gpt-4-1106-vision-preview"}, {"label": "gpt-4-vision-preview", "name": "gpt-4-vision-preview"}, {"label": "gpt-4-0613", "name": "gpt-4-0613"}, {"label": "gpt-4-32k", "name": "gpt-4-32k"}, {"label": "gpt-4-32k-0613", "name": "gpt-4-32k-0613"}, {"label": "gpt-3.5-turbo", "name": "gpt-3.5-turbo"}, {"label": "gpt-3.5-turbo-0125", "name": "gpt-3.5-turbo-0125"}, {"label": "gpt-3.5-turbo-1106", "name": "gpt-3.5-turbo-1106"}, {"label": "gpt-3.5-turbo-0613", "name": "gpt-3.5-turbo-0613"}, {"label": "gpt-3.5-turbo-16k", "name": "gpt-3.5-turbo-16k"}, {"label": "gpt-3.5-turbo-16k-0613", "name": "gpt-3.5-turbo-16k-0613"}], "default": "gpt-3.5-turbo", "optional": true, "id": "chatOpenAI_2-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_2-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_2-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_2-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_2-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-3.5-turbo-16k", "temperature": "0", "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": "", "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_2-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 669, "selected": false, "positionAbsolute": {"x": 759.2922754505641, "y": -147.3984427366269}, "dragging": false}, {"id": "chainTool_0", "position": {"x": 1521.7629525254497, "y": -103.11418698407192}, "type": "customNode", "data": {"id": "chainTool_0", "label": "Chain Tool", "version": 1, "name": "chainTool", "type": "ChainTool", "baseClasses": ["ChainTool", "DynamicTool", "Tool", "StructuredTool", "Runnable"], "category": "Tools", "description": "Use a chain as allowed tool for agent", "inputParams": [{"label": "Chain Name", "name": "name", "type": "string", "placeholder": "state-of-union-qa", "id": "chainTool_0-input-name-string"}, {"label": "Chain Description", "name": "description", "type": "string", "rows": 3, "placeholder": "State of the Union QA - useful for when you need to ask questions about the most recent state of the union address.", "id": "chainTool_0-input-description-string"}, {"label": "Return Direct", "name": "returnDirect", "type": "boolean", "optional": true, "id": "chainTool_0-input-returnDirect-boolean"}], "inputAnchors": [{"label": "Base Chain", "name": "baseChain", "type": "BaseChain", "id": "chainTool_0-input-baseChain-BaseChain"}], "inputs": {"name": "search_shirt", "description": "useful when you need to search and return answer about tshirts", "returnDirect": false, "baseChain": "{{openApiChain_0.data.instance}}"}, "outputAnchors": [{"id": "chainTool_0-output-chainTool-ChainTool|DynamicTool|Tool|StructuredTool|Runnable", "name": "chainTool", "label": "ChainTool", "description": "Use a chain as allowed tool for agent", "type": "ChainTool | DynamicTool | Tool | StructuredTool | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 602, "selected": false, "positionAbsolute": {"x": 1521.7629525254497, "y": -103.11418698407192}, "dragging": false}], "edges": [{"source": "chatOpenAI_1", "sourceHandle": "chatOpenAI_1-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "openAIToolAgent_0", "targetHandle": "openAIToolAgent_0-input-model-BaseChatModel", "type": "buttonedge", "id": "chatOpenAI_1-chatOpenAI_1-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-openAIToolAgent_0-openAIToolAgent_0-input-model-BaseChatModel"}, {"source": "bufferMemory_0", "sourceHandle": "bufferMemory_0-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory", "target": "openAIToolAgent_0", "targetHandle": "openAIToolAgent_0-input-memory-BaseChatMemory", "type": "buttonedge", "id": "bufferMemory_0-bufferMemory_0-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory-openAIToolAgent_0-openAIToolAgent_0-input-memory-BaseChatMemory"}, {"source": "chatOpenAI_2", "sourceHandle": "chatOpenAI_2-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "openApiChain_0", "targetHandle": "openApiChain_0-input-model-ChatOpenAI", "type": "buttonedge", "id": "chatOpenAI_2-chatOpenAI_2-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-openApiChain_0-openApiChain_0-input-model-ChatOpenAI"}, {"source": "openApiChain_0", "sourceHandle": "openApiChain_0-output-openApiChain-OpenAPIChain|BaseChain|Runnable", "target": "chainTool_0", "targetHandle": "chainTool_0-input-baseChain-BaseChain", "type": "buttonedge", "id": "openApiChain_0-openApiChain_0-output-openApiChain-OpenAPIChain|BaseChain|Runnable-chainTool_0-chainTool_0-input-baseChain-BaseChain"}, {"source": "chainTool_0", "sourceHandle": "chainTool_0-output-chainTool-ChainTool|DynamicTool|Tool|StructuredTool|Runnable", "target": "openAIToolAgent_0", "targetHandle": "openAIToolAgent_0-input-tools-Tool", "type": "buttonedge", "id": "chainTool_0-chainTool_0-output-chainTool-ChainTool|DynamicTool|Tool|StructuredTool|Runnable-openAIToolAgent_0-openAIToolAgent_0-input-tools-Tool"}]}