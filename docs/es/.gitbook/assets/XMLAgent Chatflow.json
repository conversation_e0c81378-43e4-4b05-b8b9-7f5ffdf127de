{"nodes": [{"width": 300, "height": 376, "id": "bufferMemory_0", "position": {"x": 363.18868341411155, "y": 473.71555789686244}, "type": "customNode", "data": {"id": "bufferMemory_0", "label": "Buffer Memory", "version": 1, "name": "bufferMemory", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BaseChatMemory", "BaseMemory"], "category": "Memory", "description": "Remembers previous conversational back and forths directly", "inputParams": [{"label": "Memory Key", "name": "<PERSON><PERSON><PERSON>", "type": "string", "default": "chat_history", "id": "bufferMemory_0-input-memoryKey-string"}, {"label": "Input Key", "name": "inputKey", "type": "string", "default": "input", "id": "bufferMemory_0-input-inputKey-string"}], "inputAnchors": [], "inputs": {"memoryKey": "chat_history", "inputKey": "input"}, "outputAnchors": [{"id": "bufferMemory_0-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory", "name": "bufferMemory", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "BufferMemory | BaseChatMemory | BaseMemory"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 363.18868341411155, "y": 473.71555789686244}, "dragging": false}, {"id": "xmlAgent_0", "position": {"x": 1492.5028255133452, "y": 161.2653235031646}, "type": "customNode", "data": {"id": "xmlAgent_0", "label": "XML Agent", "version": 2, "name": "xmlAgent", "type": "XMLAgent", "baseClasses": ["XMLAgent", "BaseChain", "Runnable"], "category": "Agents", "description": "Agent that is designed for LLMs that are good for reasoning/writing XML (e.g: <PERSON><PERSON><PERSON> <PERSON>)", "inputParams": [{"label": "System Message", "name": "systemMessage", "type": "string", "warning": "Prompt must include input variables: {tools}, {chat_history}, {input} and {agent_scratchpad}", "rows": 4, "default": "You are a helpful assistant. Help the user answer any questions.\n\nYou have access to the following tools:\n\n{tools}\n\nIn order to use a tool, you can use <tool></tool> and <tool_input></tool_input> tags. You will then get back a response in the form <observation></observation>\nFor example, if you have a tool called 'search' that could run a google search, in order to search for the weather in SF you would respond:\n\n<tool>search</tool><tool_input>weather in SF</tool_input>\n<observation>64 degrees</observation>\n\nWhen you are done, respond with a final answer between <final_answer></final_answer>. For example:\n\n<final_answer>The weather in SF is 64 degrees</final_answer>\n\nBegin!\n\nPrevious Conversation:\n{chat_history}\n\nQuestion: {input}\n{agent_scratchpad}", "additionalParams": true, "id": "xmlAgent_0-input-systemMessage-string"}], "inputAnchors": [{"label": "Tools", "name": "tools", "type": "Tool", "list": true, "id": "xmlAgent_0-input-tools-Tool"}, {"label": "Memory", "name": "memory", "type": "BaseChatMemory", "id": "xmlAgent_0-input-memory-BaseChatMemory"}, {"label": "Chat Model", "name": "model", "type": "BaseChatModel", "id": "xmlAgent_0-input-model-BaseChatModel"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "xmlAgent_0-input-inputModeration-Moderation"}], "inputs": {"tools": ["{{retrieverTool_0.data.instance}}", "{{retrieverTool_1.data.instance}}"], "memory": "{{bufferMemory_0.data.instance}}", "model": "{{chatAnthropic_0.data.instance}}", "systemMessage": "You are a helpful assistant. Help the user answer any questions.\n\nYou have access to the following tools:\n\n{tools}\n\nIn order to use a tool, you can use <tool></tool> and <tool_input></tool_input> tags. You will then get back a response in the form <observation></observation>\nFor example, if you have a tool called 'search' that could run a google search, in order to search for the weather in SF you would respond:\n\n<tool>search</tool><tool_input>weather in SF</tool_input>\n<observation>64 degrees</observation>\n\nWhen you are done, respond with a final answer between <final_answer></final_answer>. For example:\n\n<final_answer>The weather in SF is 64 degrees</final_answer>\n\nBegin!\n\nPrevious Conversation:\n{chat_history}\n\nQuestion: {input}\n{agent_scratchpad}", "inputModeration": ""}, "outputAnchors": [{"id": "xmlAgent_0-output-xmlAgent-XMLAgent|BaseChain|Runnable", "name": "xmlAgent", "label": "XMLAgent", "description": "Agent that is designed for LLMs that are good for reasoning/writing XML (e.g: <PERSON><PERSON><PERSON> <PERSON>)", "type": "XMLAgent | BaseChain | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 434, "selected": false, "positionAbsolute": {"x": 1492.5028255133452, "y": 161.2653235031646}, "dragging": false}, {"id": "chatAnthropic_0", "position": {"x": 1073.3453545616378, "y": 298.65772549403795}, "type": "customNode", "data": {"id": "chatAnthropic_0", "label": "ChatAnthropic", "version": 5, "name": "chatAnthropic", "type": "ChatAnthropic", "baseClasses": ["ChatAnthropic", "ChatAnthropicMessages", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around ChatAnthropic large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["anthropicApi"], "id": "chatAnthropic_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "options", "options": [{"label": "claude-3-haiku", "name": "claude-3-haiku-20240307", "description": "Fastest and most compact model, designed for near-instant responsiveness"}, {"label": "claude-3-opus", "name": "claude-3-opus-20240229", "description": "Most powerful model for highly complex tasks"}, {"label": "claude-3-sonnet", "name": "claude-3-sonnet-20240229", "description": "Ideal balance of intelligence and speed for enterprise workloads"}, {"label": "claude-2.0 (legacy)", "name": "claude-2.0", "description": "Claude 2 latest major version, automatically get updates to the model as they are released"}, {"label": "claude-2.1 (legacy)", "name": "claude-2.1", "description": "Claude 2 latest full version"}, {"label": "claude-instant-1.2 (legacy)", "name": "claude-instant-1.2", "description": "Claude Instant latest major version, automatically get updates to the model as they are released"}], "default": "claude-3-haiku", "optional": true, "id": "chatAnthropic_0-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatAnthropic_0-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokensToSample", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatAnthropic_0-input-maxTokensToSample-number"}, {"label": "Top P", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatAnthropic_0-input-topP-number"}, {"label": "Top K", "name": "topK", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatAnthropic_0-input-topK-number"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses claude-3-* models when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatAnthropic_0-input-allowImageUploads-boolean"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatAnthropic_0-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "claude-3-sonnet-20240229", "temperature": "0", "maxTokensToSample": "", "topP": "", "topK": "", "allowImageUploads": ""}, "outputAnchors": [{"id": "chatAnthropic_0-output-chatAnthropic-ChatAnthropic|ChatAnthropicMessages|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatAnthropic", "label": "ChatAnthropic", "description": "Wrapper around ChatAnthropic large language models that use the Chat endpoint", "type": "ChatAnthropic | ChatAnthropicMessages | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 671, "selected": false, "positionAbsolute": {"x": 1073.3453545616378, "y": 298.65772549403795}, "dragging": false}, {"id": "retrieverTool_0", "position": {"x": 716.1176116429738, "y": -19.654771005396583}, "type": "customNode", "data": {"id": "retrieverTool_0", "label": "Retriever Tool", "version": 2, "name": "retrieverTool", "type": "RetrieverTool", "baseClasses": ["RetrieverTool", "DynamicTool", "Tool", "StructuredTool", "Runnable"], "category": "Tools", "description": "Use a retriever as allowed tool for agent", "inputParams": [{"label": "Retriever Name", "name": "name", "type": "string", "placeholder": "search_state_of_union", "id": "retrieverTool_0-input-name-string"}, {"label": "Retriever Description", "name": "description", "type": "string", "description": "When should agent uses to retrieve documents", "rows": 3, "placeholder": "Searches and returns documents regarding the state-of-the-union.", "id": "retrieverTool_0-input-description-string"}, {"label": "Return Source Documents", "name": "returnSourceDocuments", "type": "boolean", "optional": true, "id": "retrieverTool_0-input-returnSourceDocuments-boolean"}], "inputAnchors": [{"label": "Retriever", "name": "retriever", "type": "BaseRetriever", "id": "retrieverTool_0-input-retriever-BaseRetriever"}], "inputs": {"name": "search_apple", "description": "Use this function to answer user questions about Apple Inc (APPL). It contains a SEC Form 10K filing describing the financials of Apple Inc (APPL) for the 2022 time period.", "retriever": "{{pinecone_0.data.instance}}", "returnSourceDocuments": true}, "outputAnchors": [{"id": "retrieverTool_0-output-retrieverTool-RetrieverTool|DynamicTool|Tool|StructuredTool|Runnable", "name": "retrieverTool", "label": "RetrieverTool", "description": "Use a retriever as allowed tool for agent", "type": "RetrieverTool | DynamicTool | Tool | StructuredTool | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 602, "selected": false, "positionAbsolute": {"x": 716.1176116429738, "y": -19.654771005396583}, "dragging": false}, {"id": "pinecone_0", "position": {"x": 378.7110351151108, "y": -122.59235389975663}, "type": "customNode", "data": {"id": "pinecone_0", "label": "Pinecone", "version": 2, "name": "pinecone", "type": "Pinecone", "baseClasses": ["Pinecone", "VectorStoreRetriever", "BaseRetriever"], "category": "Vector Stores", "description": "Upsert embedded data and perform similarity or mmr search using Pinecone, a leading fully managed hosted vector database", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["pineconeApi"], "id": "pinecone_0-input-credential-credential"}, {"label": "Pinecone Index", "name": "pineconeIndex", "type": "string", "id": "pinecone_0-input-pineconeIndex-string"}, {"label": "Pinecone Namespace", "name": "pineconeNamespace", "type": "string", "placeholder": "my-first-namespace", "additionalParams": true, "optional": true, "id": "pinecone_0-input-pineconeNamespace-string"}, {"label": "Pinecone Metadata Filter", "name": "pineconeMetadataFilter", "type": "json", "optional": true, "additionalParams": true, "id": "pinecone_0-input-pineconeMetadataFilter-json"}, {"label": "Top K", "name": "topK", "description": "Number of top results to fetch. De<PERSON><PERSON> to 4", "placeholder": "4", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_0-input-topK-number"}, {"label": "Search Type", "name": "searchType", "type": "options", "default": "similarity", "options": [{"label": "Similarity", "name": "similarity"}, {"label": "<PERSON>ginal Relevance", "name": "mmr"}], "additionalParams": true, "optional": true, "id": "pinecone_0-input-searchType-options"}, {"label": "Fetch K (for MMR Search)", "name": "fetchK", "description": "Number of initial documents to fetch for MMR reranking. Default to 20. Used only when the search type is MMR", "placeholder": "20", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_0-input-fetchK-number"}, {"label": "Lambda (for MMR Search)", "name": "lambda", "description": "Number between 0 and 1 that determines the degree of diversity among the results, where 0 corresponds to maximum diversity and 1 to minimum diversity. Used only when the search type is MMR", "placeholder": "0.5", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_0-input-lambda-number"}], "inputAnchors": [{"label": "Document", "name": "document", "type": "Document", "list": true, "optional": true, "id": "pinecone_0-input-document-Document"}, {"label": "Embeddings", "name": "embeddings", "type": "Embeddings", "id": "pinecone_0-input-embeddings-Embeddings"}], "inputs": {"document": "", "embeddings": "{{openAIEmbeddings_0.data.instance}}", "pineconeIndex": "flowiseindex", "pineconeNamespace": "pinecone-form10k", "pineconeMetadataFilter": "{\"source\":\"apple\"}", "topK": "", "searchType": "similarity", "fetchK": "", "lambda": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "description": "", "options": [{"id": "pinecone_0-output-retriever-Pinecone|VectorStoreRetriever|BaseRetriever", "name": "retriever", "label": "Pinecone Retriever", "description": "", "type": "Pinecone | VectorStoreRetriever | BaseRetriever"}, {"id": "pinecone_0-output-vectorStore-Pinecone|VectorStore", "name": "vectorStore", "label": "Pinecone Vector Store", "description": "", "type": "Pinecone | VectorStore"}], "default": "retriever"}], "outputs": {"output": "retriever"}, "selected": false}, "width": 300, "height": 555, "selected": false, "positionAbsolute": {"x": 378.7110351151108, "y": -122.59235389975663}, "dragging": false}, {"id": "openAIEmbeddings_0", "position": {"x": -12.140161701463555, "y": -54.69898887572853}, "type": "customNode", "data": {"id": "openAIEmbeddings_0", "label": "OpenAI Embeddings", "version": 2, "name": "openAIEmbeddings", "type": "OpenAIEmbeddings", "baseClasses": ["OpenAIEmbeddings", "Embeddings"], "category": "Embeddings", "description": "OpenAI API to generate embeddings for a given text", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "openAIEmbeddings_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "options", "options": [{"label": "text-embedding-3-large", "name": "text-embedding-3-large"}, {"label": "text-embedding-3-small", "name": "text-embedding-3-small"}, {"label": "text-embedding-ada-002", "name": "text-embedding-ada-002"}], "default": "text-embedding-ada-002", "optional": true, "id": "openAIEmbeddings_0-input-modelName-options"}, {"label": "Strip New Lines", "name": "stripNewLines", "type": "boolean", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-stripNewLines-boolean"}, {"label": "<PERSON><PERSON> Si<PERSON>", "name": "batchSize", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-batchSize-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-basepath-string"}], "inputAnchors": [], "inputs": {"modelName": "text-embedding-ada-002", "stripNewLines": "", "batchSize": "", "timeout": "", "basepath": ""}, "outputAnchors": [{"id": "openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "name": "openAIEmbeddings", "label": "OpenAIEmbeddings", "description": "OpenAI API to generate embeddings for a given text", "type": "OpenAIEmbeddings | Embeddings"}], "outputs": {}, "selected": false}, "width": 300, "height": 425, "selected": false, "positionAbsolute": {"x": -12.140161701463555, "y": -54.69898887572853}, "dragging": false}, {"id": "retrieverTool_1", "position": {"x": 1095.1510607975026, "y": -326.06593550420894}, "type": "customNode", "data": {"id": "retrieverTool_1", "label": "Retriever Tool", "version": 2, "name": "retrieverTool", "type": "RetrieverTool", "baseClasses": ["RetrieverTool", "DynamicTool", "Tool", "StructuredTool", "Runnable"], "category": "Tools", "description": "Use a retriever as allowed tool for agent", "inputParams": [{"label": "Retriever Name", "name": "name", "type": "string", "placeholder": "search_state_of_union", "id": "retrieverTool_1-input-name-string"}, {"label": "Retriever Description", "name": "description", "type": "string", "description": "When should agent uses to retrieve documents", "rows": 3, "placeholder": "Searches and returns documents regarding the state-of-the-union.", "id": "retrieverTool_1-input-description-string"}, {"label": "Return Source Documents", "name": "returnSourceDocuments", "type": "boolean", "optional": true, "id": "retrieverTool_1-input-returnSourceDocuments-boolean"}], "inputAnchors": [{"label": "Retriever", "name": "retriever", "type": "BaseRetriever", "id": "retrieverTool_1-input-retriever-BaseRetriever"}], "inputs": {"name": "search_tsla", "description": "Use this function to answer user questions about Tesla Inc (TSLA). It contains a SEC Form 10K filing describing the financials of Tesla Inc (TSLA) for the 2022 time period.", "retriever": "{{pinecone_1.data.instance}}", "returnSourceDocuments": true}, "outputAnchors": [{"id": "retrieverTool_1-output-retrieverTool-RetrieverTool|DynamicTool|Tool|StructuredTool|Runnable", "name": "retrieverTool", "label": "RetrieverTool", "description": "Use a retriever as allowed tool for agent", "type": "RetrieverTool | DynamicTool | Tool | StructuredTool | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 602, "selected": false, "positionAbsolute": {"x": 1095.1510607975026, "y": -326.06593550420894}, "dragging": false}, {"id": "pinecone_1", "position": {"x": 762.2373165936692, "y": -619.4996089374204}, "type": "customNode", "data": {"id": "pinecone_1", "label": "Pinecone", "version": 2, "name": "pinecone", "type": "Pinecone", "baseClasses": ["Pinecone", "VectorStoreRetriever", "BaseRetriever"], "category": "Vector Stores", "description": "Upsert embedded data and perform similarity or mmr search using Pinecone, a leading fully managed hosted vector database", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["pineconeApi"], "id": "pinecone_1-input-credential-credential"}, {"label": "Pinecone Index", "name": "pineconeIndex", "type": "string", "id": "pinecone_1-input-pineconeIndex-string"}, {"label": "Pinecone Namespace", "name": "pineconeNamespace", "type": "string", "placeholder": "my-first-namespace", "additionalParams": true, "optional": true, "id": "pinecone_1-input-pineconeNamespace-string"}, {"label": "Pinecone Metadata Filter", "name": "pineconeMetadataFilter", "type": "json", "optional": true, "additionalParams": true, "id": "pinecone_1-input-pineconeMetadataFilter-json"}, {"label": "Top K", "name": "topK", "description": "Number of top results to fetch. De<PERSON><PERSON> to 4", "placeholder": "4", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_1-input-topK-number"}, {"label": "Search Type", "name": "searchType", "type": "options", "default": "similarity", "options": [{"label": "Similarity", "name": "similarity"}, {"label": "<PERSON>ginal Relevance", "name": "mmr"}], "additionalParams": true, "optional": true, "id": "pinecone_1-input-searchType-options"}, {"label": "Fetch K (for MMR Search)", "name": "fetchK", "description": "Number of initial documents to fetch for MMR reranking. Default to 20. Used only when the search type is MMR", "placeholder": "20", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_1-input-fetchK-number"}, {"label": "Lambda (for MMR Search)", "name": "lambda", "description": "Number between 0 and 1 that determines the degree of diversity among the results, where 0 corresponds to maximum diversity and 1 to minimum diversity. Used only when the search type is MMR", "placeholder": "0.5", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_1-input-lambda-number"}], "inputAnchors": [{"label": "Document", "name": "document", "type": "Document", "list": true, "optional": true, "id": "pinecone_1-input-document-Document"}, {"label": "Embeddings", "name": "embeddings", "type": "Embeddings", "id": "pinecone_1-input-embeddings-Embeddings"}], "inputs": {"document": "", "embeddings": "{{openAIEmbeddings_1.data.instance}}", "pineconeIndex": "flowiseindex", "pineconeNamespace": "pinecone-form10k", "pineconeMetadataFilter": "{\"source\":\"tesla\"}", "topK": "", "searchType": "similarity", "fetchK": "", "lambda": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "description": "", "options": [{"id": "pinecone_1-output-retriever-Pinecone|VectorStoreRetriever|BaseRetriever", "name": "retriever", "label": "Pinecone Retriever", "description": "", "type": "Pinecone | VectorStoreRetriever | BaseRetriever"}, {"id": "pinecone_1-output-vectorStore-Pinecone|VectorStore", "name": "vectorStore", "label": "Pinecone Vector Store", "description": "", "type": "Pinecone | VectorStore"}], "default": "retriever"}], "outputs": {"output": "retriever"}, "selected": false}, "width": 300, "height": 555, "selected": false, "positionAbsolute": {"x": 762.2373165936692, "y": -619.4996089374204}, "dragging": false}, {"id": "openAIEmbeddings_1", "position": {"x": 416.3144430173899, "y": -599.2302665481047}, "type": "customNode", "data": {"id": "openAIEmbeddings_1", "label": "OpenAI Embeddings", "version": 2, "name": "openAIEmbeddings", "type": "OpenAIEmbeddings", "baseClasses": ["OpenAIEmbeddings", "Embeddings"], "category": "Embeddings", "description": "OpenAI API to generate embeddings for a given text", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "openAIEmbeddings_1-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "options", "options": [{"label": "text-embedding-3-large", "name": "text-embedding-3-large"}, {"label": "text-embedding-3-small", "name": "text-embedding-3-small"}, {"label": "text-embedding-ada-002", "name": "text-embedding-ada-002"}], "default": "text-embedding-ada-002", "optional": true, "id": "openAIEmbeddings_1-input-modelName-options"}, {"label": "Strip New Lines", "name": "stripNewLines", "type": "boolean", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_1-input-stripNewLines-boolean"}, {"label": "<PERSON><PERSON> Si<PERSON>", "name": "batchSize", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_1-input-batchSize-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_1-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_1-input-basepath-string"}], "inputAnchors": [], "inputs": {"modelName": "text-embedding-ada-002", "stripNewLines": "", "batchSize": "", "timeout": "", "basepath": ""}, "outputAnchors": [{"id": "openAIEmbeddings_1-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "name": "openAIEmbeddings", "label": "OpenAIEmbeddings", "description": "OpenAI API to generate embeddings for a given text", "type": "OpenAIEmbeddings | Embeddings"}], "outputs": {}, "selected": false}, "width": 300, "height": 425, "selected": false, "positionAbsolute": {"x": 416.3144430173899, "y": -599.2302665481047}, "dragging": false}], "edges": [{"source": "retrieverTool_0", "sourceHandle": "retrieverTool_0-output-retrieverTool-RetrieverTool|DynamicTool|Tool|StructuredTool|Runnable", "target": "xmlAgent_0", "targetHandle": "xmlAgent_0-input-tools-Tool", "type": "buttonedge", "id": "retrieverTool_0-retrieverTool_0-output-retrieverTool-RetrieverTool|DynamicTool|Tool|StructuredTool|Runnable-xmlAgent_0-xmlAgent_0-input-tools-Tool"}, {"source": "chatAnthropic_0", "sourceHandle": "chatAnthropic_0-output-chatAnthropic-ChatAnthropic|ChatAnthropicMessages|BaseChatModel|BaseLanguageModel|Runnable", "target": "xmlAgent_0", "targetHandle": "xmlAgent_0-input-model-BaseChatModel", "type": "buttonedge", "id": "chatAnthropic_0-chatAnthropic_0-output-chatAnthropic-ChatAnthropic|ChatAnthropicMessages|BaseChatModel|BaseLanguageModel|Runnable-xmlAgent_0-xmlAgent_0-input-model-BaseChatModel"}, {"source": "bufferMemory_0", "sourceHandle": "bufferMemory_0-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory", "target": "xmlAgent_0", "targetHandle": "xmlAgent_0-input-memory-BaseChatMemory", "type": "buttonedge", "id": "bufferMemory_0-bufferMemory_0-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory-xmlAgent_0-xmlAgent_0-input-memory-BaseChatMemory"}, {"source": "openAIEmbeddings_0", "sourceHandle": "openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "target": "pinecone_0", "targetHandle": "pinecone_0-input-embeddings-Embeddings", "type": "buttonedge", "id": "openAIEmbeddings_0-openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings-pinecone_0-pinecone_0-input-embeddings-Embeddings"}, {"source": "pinecone_0", "sourceHandle": "pinecone_0-output-retriever-Pinecone|VectorStoreRetriever|BaseRetriever", "target": "retrieverTool_0", "targetHandle": "retrieverTool_0-input-retriever-BaseRetriever", "type": "buttonedge", "id": "pinecone_0-pinecone_0-output-retriever-<PERSON>con<PERSON>|VectorStoreRetriever|BaseRetriever-retrieverTool_0-retrieverTool_0-input-retriever-BaseRetriever"}, {"source": "retrieverTool_1", "sourceHandle": "retrieverTool_1-output-retrieverTool-RetrieverTool|DynamicTool|Tool|StructuredTool|Runnable", "target": "xmlAgent_0", "targetHandle": "xmlAgent_0-input-tools-Tool", "type": "buttonedge", "id": "retrieverTool_1-retrieverTool_1-output-retrieverTool-RetrieverTool|DynamicTool|Tool|StructuredTool|Runnable-xmlAgent_0-xmlAgent_0-input-tools-Tool"}, {"source": "openAIEmbeddings_1", "sourceHandle": "openAIEmbeddings_1-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "target": "pinecone_1", "targetHandle": "pinecone_1-input-embeddings-Embeddings", "type": "buttonedge", "id": "openAIEmbeddings_1-openAIEmbeddings_1-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings-pinecone_1-pinecone_1-input-embeddings-Embeddings"}, {"source": "pinecone_1", "sourceHandle": "pinecone_1-output-retriever-Pinecone|VectorStoreRetriever|BaseRetriever", "target": "retrieverTool_1", "targetHandle": "retrieverTool_1-input-retriever-BaseRetriever", "type": "buttonedge", "id": "pinecone_1-pinecone_1-output-retriever-<PERSON>con<PERSON>|VectorStoreRetriever|BaseRetriever-retrieverTool_1-retrieverTool_1-input-retriever-BaseRetriever"}]}