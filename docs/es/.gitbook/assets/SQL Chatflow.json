{"nodes": [{"width": 300, "height": 779, "id": "promptTemplate_0", "position": {"x": 379.11224395092825, "y": 71.33564730890853}, "type": "customNode", "data": {"id": "promptTemplate_0", "label": "Prompt Template", "version": 1, "name": "promptTemplate", "type": "PromptTemplate", "baseClasses": ["PromptTemplate", "BaseStringPromptTemplate", "BasePromptTemplate", "Runnable"], "category": "Prompts", "description": "Schema to represent a basic prompt for an LLM", "inputParams": [{"label": "Template", "name": "template", "type": "string", "rows": 4, "placeholder": "What is a good name for a company that makes {product}?", "id": "promptTemplate_0-input-template-string"}, {"label": "Format Prompt Values", "name": "promptValues", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "promptTemplate_0-input-promptValues-json"}], "inputAnchors": [], "inputs": {"template": "Based on the provided SQL table schema and question below, return a SQL SELECT ALL query that would answer the user's question. For example: SELECT * FROM table WHERE id = '1'.\n------------\nSCHEMA: {schema}\n------------\nQUESTION: {question}\n------------\nSQL QUERY:", "promptValues": "{\"schema\":\"{{customFunction_2.data.instance}}\",\"question\":\"{{question}}\"}"}, "outputAnchors": [{"id": "promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable", "name": "promptTemplate", "label": "PromptTemplate", "type": "PromptTemplate | BaseStringPromptTemplate | BasePromptTemplate | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 379.11224395092825, "y": 71.33564730890853}, "dragging": false}, {"width": 300, "height": 506, "id": "llmChain_0", "position": {"x": 770.4559230968546, "y": -127.11351409346554}, "type": "customNode", "data": {"id": "llmChain_0", "label": "LLM Chain", "version": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON>", "BaseChain", "Runnable"], "category": "Chains", "description": "Chain to run queries against LLMs", "inputParams": [{"label": "Chain Name", "name": "chainName", "type": "string", "placeholder": "Name Your Chain", "optional": true, "id": "llmChain_0-input-chainName-string"}], "inputAnchors": [{"label": "Language Model", "name": "model", "type": "BaseLanguageModel", "id": "llmChain_0-input-model-BaseLanguageModel"}, {"label": "Prompt", "name": "prompt", "type": "BasePromptTemplate", "id": "llmChain_0-input-prompt-BasePromptTemplate"}, {"label": "Output Parser", "name": "outputParser", "type": "BaseLLMOutputParser", "optional": true, "id": "llmChain_0-input-outputParser-BaseLLMOutputParser"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "llmChain_0-input-inputModeration-Moderation"}], "inputs": {"model": "{{chatOpenAI_1.data.instance}}", "prompt": "{{promptTemplate_0.data.instance}}", "outputParser": "", "inputModeration": "", "chainName": "SQL Query Chain"}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "llmChain_0-output-llmChain-LLMChain|BaseChain|Runnable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "LLM Chain", "type": "LLMChain | BaseChain | Runnable"}, {"id": "llmChain_0-output-outputPrediction-string|json", "name": "outputPrediction", "label": "Output Prediction", "type": "string | json"}], "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "outputs": {"output": "outputPrediction"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 770.4559230968546, "y": -127.11351409346554}, "dragging": false}, {"width": 300, "height": 506, "id": "llmChain_1", "position": {"x": 2330.1281944523407, "y": -325.61633963017937}, "type": "customNode", "data": {"id": "llmChain_1", "label": "LLM Chain", "version": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON>", "BaseChain", "Runnable"], "category": "Chains", "description": "Chain to run queries against LLMs", "inputParams": [{"label": "Chain Name", "name": "chainName", "type": "string", "placeholder": "Name Your Chain", "optional": true, "id": "llmChain_1-input-chainName-string"}], "inputAnchors": [{"label": "Language Model", "name": "model", "type": "BaseLanguageModel", "id": "llmChain_1-input-model-BaseLanguageModel"}, {"label": "Prompt", "name": "prompt", "type": "BasePromptTemplate", "id": "llmChain_1-input-prompt-BasePromptTemplate"}, {"label": "Output Parser", "name": "outputParser", "type": "BaseLLMOutputParser", "optional": true, "id": "llmChain_1-input-outputParser-BaseLLMOutputParser"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "llmChain_1-input-inputModeration-Moderation"}], "inputs": {"model": "{{chatOpenAI_3.data.instance}}", "prompt": "{{promptTemplate_1.data.instance}}", "outputParser": "", "inputModeration": "", "chainName": "Final Chain"}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "llmChain_1-output-llmChain-LLMChain|BaseChain|Runnable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "LLM Chain", "type": "LLMChain | BaseChain | Runnable"}, {"id": "llmChain_1-output-outputPrediction-string|json", "name": "outputPrediction", "label": "Output Prediction", "type": "string | json"}], "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "outputs": {"output": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 2330.1281944523407, "y": -325.61633963017937}, "dragging": false}, {"width": 300, "height": 668, "id": "customFunction_2", "position": {"x": -12.41348902418423, "y": -198.08456515277106}, "type": "customNode", "data": {"id": "customFunction_2", "label": "Custom JS Function", "version": 1, "name": "customFunction", "type": "CustomFunction", "baseClasses": ["CustomFunction", "Utilities"], "category": "Utilities", "description": "Execute custom javascript function", "inputParams": [{"label": "Input Variables", "name": "functionInputVariables", "description": "Input variables can be used in the function with prefix $. For example: $var", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "customFunction_2-input-functionInputVariables-json"}, {"label": "Function Name", "name": "functionName", "type": "string", "placeholder": "My Function", "id": "customFunction_2-input-functionName-string"}, {"label": "Javascript Function", "name": "javascriptFunction", "type": "code", "id": "customFunction_2-input-javascriptFunction-code"}], "inputAnchors": [], "inputs": {"functionInputVariables": "", "functionName": "Get SQL Schema Prompt", "javascriptFunction": "const HOST = 'svc-abc.aws-oregon-3.svc.singlestore.com';\nconst USER = 'admin';\nconst PASSWORD = '123';\nconst DATABASE = 'mydb';\nconst TABLE = 'samples';\nconst mysql = require('mysql2/promise');\n\nlet sqlSchemaPrompt;\n\n/**\n * Ideal prompt contains schema info and examples\n * Follows best practices as specified form https://arxiv.org/abs/2204.00498\n * =========================================\n * CREATE TABLE samples (firstName varchar NOT NULL, lastName varchar)\n * SELECT * FROM samples LIMIT 3\n * firstName lastName\n * Stephen <PERSON>\n * Jack <PERSON>c<PERSON>innis\n * Steven Repici\n * =========================================\n*/\nfunction getSQLPrompt() {\n  return new Promise(async (resolve, reject) => {\n    try {\n      const singleStoreConnection = mysql.createPool({\n        host: HOST,\n        user: USER,\n        password: PASSWORD,\n        database: DATABASE,\n      });\n  \n      // Get schema info\n      const [schemaInfo] = await singleStoreConnection.execute(\n        `SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = \"${TABLE}\"`\n      );\n  \n      const createColumns = [];\n      const columnNames = [];\n  \n      for (const schemaData of schemaInfo) {\n        columnNames.push(`${schemaData['COLUMN_NAME']}`);\n        createColumns.push(`${schemaData['COLUMN_NAME']} ${schemaData['COLUMN_TYPE']} ${schemaData['IS_NULLABLE'] === 'NO' ? 'NOT NULL' : ''}`);\n      }\n  \n      const sqlCreateTableQuery = `CREATE TABLE samples (${createColumns.join(', ')})`;\n      const sqlSelectTableQuery = `SELECT * FROM samples LIMIT 3`;\n  \n      // Get first 3 rows\n      const [rows] = await singleStoreConnection.execute(\n          sqlSelectTableQuery,\n      );\n      \n      const allValues = [];\n      for (const row of rows) {\n          const rowValues = [];\n          for (const colName in row) {\n              rowValues.push(row[colName]);\n          }\n          allValues.push(rowValues.join(' '));\n      }\n  \n      sqlSchemaPrompt = sqlCreateTableQuery + '\\n' + sqlSelectTableQuery + '\\n' + columnNames.join(' ') + '\\n' + allValues.join('\\n');\n      \n      resolve();\n    } catch (e) {\n      console.error(e);\n      return reject(e);\n    }\n  });\n}\n\nasync function main() {\n    await getSQLPrompt();\n}\n\nawait main();\n\nreturn sqlSchemaPrompt;"}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "customFunction_2-output-output-string|number|boolean|json|array", "name": "output", "label": "Output", "type": "string | number | boolean | json | array"}], "default": "output"}], "outputs": {"output": "output"}, "selected": false}, "selected": false, "positionAbsolute": {"x": -12.41348902418423, "y": -198.08456515277106}, "dragging": false}, {"width": 300, "height": 668, "id": "customFunction_1", "position": {"x": 1574.8757854291205, "y": -510.612625067788}, "type": "customNode", "data": {"id": "customFunction_1", "label": "Custom JS Function", "version": 1, "name": "customFunction", "type": "CustomFunction", "baseClasses": ["CustomFunction", "Utilities"], "category": "Utilities", "description": "Execute custom javascript function", "inputParams": [{"label": "Input Variables", "name": "functionInputVariables", "description": "Input variables can be used in the function with prefix $. For example: $var", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "customFunction_1-input-functionInputVariables-json"}, {"label": "Function Name", "name": "functionName", "type": "string", "placeholder": "My Function", "id": "customFunction_1-input-functionName-string"}, {"label": "Javascript Function", "name": "javascriptFunction", "type": "code", "id": "customFunction_1-input-javascriptFunction-code"}], "inputAnchors": [], "inputs": {"functionInputVariables": "{\"sqlQuery\":\"{{ifElseFunction_0.data.instance}}\"}", "functionName": "Run SQL Query", "javascriptFunction": "const HOST = 'svc-abc.aws-oregon-3.svc.singlestore.com';\nconst USER = 'admin';\nconst PASSWORD = '123';\nconst DATABASE = 'mydb';\nconst TABLE = 'samples';\nconst mysql = require('mysql2/promise');\n\nlet result;\n\nfunction getSQLResult() {\n  return new Promise(async (resolve, reject) => {\n    try {\n      const singleStoreConnection = mysql.createPool({\n        host: HOST,\n        user: USER,\n        password: PASSWORD,\n        database: DATABASE,\n      });\n     \n      const [rows] = await singleStoreConnection.execute(\n        $sqlQuery\n      );\n  \n      result = JSON.stringify(rows)\n      \n      resolve();\n    } catch (e) {\n      console.error(e);\n      return reject(e);\n    }\n  });\n}\n\nasync function main() {\n    await getSQLResult();\n}\n\nawait main();\n\nreturn result;"}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "customFunction_1-output-output-string|number|boolean|json|array", "name": "output", "label": "Output", "type": "string | number | boolean | json | array"}], "default": "output"}], "outputs": {"output": "output"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1574.8757854291205, "y": -510.612625067788}, "dragging": false}, {"width": 300, "height": 779, "id": "promptTemplate_1", "position": {"x": 1943.5658568848553, "y": -83.07909710675825}, "type": "customNode", "data": {"id": "promptTemplate_1", "label": "Prompt Template", "version": 1, "name": "promptTemplate", "type": "PromptTemplate", "baseClasses": ["PromptTemplate", "BaseStringPromptTemplate", "BasePromptTemplate", "Runnable"], "category": "Prompts", "description": "Schema to represent a basic prompt for an LLM", "inputParams": [{"label": "Template", "name": "template", "type": "string", "rows": 4, "placeholder": "What is a good name for a company that makes {product}?", "id": "promptTemplate_1-input-template-string"}, {"label": "Format Prompt Values", "name": "promptValues", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "promptTemplate_1-input-promptValues-json"}], "inputAnchors": [], "inputs": {"template": "Based on the question, and SQL response, write a natural language response, be details as possible:\n------------\nQUESTION: {question}\n------------\nSQL RESPONSE: {sqlResponse}\n------------\nNATURAL LANGUAGE RESPONSE:", "promptValues": "{\"question\":\"{{question}}\",\"sqlResponse\":\"{{customFunction_1.data.instance}}\"}"}, "outputAnchors": [{"id": "promptTemplate_1-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable", "name": "promptTemplate", "label": "PromptTemplate", "type": "PromptTemplate | BaseStringPromptTemplate | BasePromptTemplate | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "dragging": false, "positionAbsolute": {"x": 1943.5658568848553, "y": -83.07909710675825}}, {"width": 300, "height": 754, "id": "ifElseFunction_0", "position": {"x": 1217.9173937613534, "y": -515.3650041424041}, "type": "customNode", "data": {"id": "ifElseFunction_0", "label": "IfElse Function", "version": 1, "name": "ifElseFunction", "type": "IfElseFunction", "baseClasses": ["IfElseFunction", "Utilities"], "category": "Utilities", "description": "Split flows based on If Else javascript functions", "inputParams": [{"label": "Input Variables", "name": "functionInputVariables", "description": "Input variables can be used in the function with prefix $. For example: $var", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "ifElseFunction_0-input-functionInputVariables-json"}, {"label": "IfElse Name", "name": "functionName", "type": "string", "optional": true, "placeholder": "If Condition Match", "id": "ifElseFunction_0-input-functionName-string"}, {"label": "If Function", "name": "ifFunction", "description": "Function must return a value", "type": "code", "rows": 2, "default": "if (\"hello\" == \"hello\") {\n    return true;\n}", "id": "ifElseFunction_0-input-ifFunction-code"}, {"label": "Else Function", "name": "elseFunction", "description": "Function must return a value", "type": "code", "rows": 2, "default": "return false;", "id": "ifElseFunction_0-input-elseFunction-code"}], "inputAnchors": [], "inputs": {"functionInputVariables": "{\"sqlQuery\":\"{{llmChain_0.data.instance}}\"}", "functionName": "IF SQL Query contains SELECT and WHERE", "ifFunction": "const sqlQuery = $sqlQuery.trim();\n\nif (sqlQuery.includes(\"SELECT\") && sqlQuery.includes(\"WHERE\")) {\n    return sqlQuery;\n}", "elseFunction": "return $sqlQuery;"}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "ifElseFunction_0-output-returnTrue-string|number|boolean|json|array", "name": "returnTrue", "label": "True", "type": "string | number | boolean | json | array"}, {"id": "ifElseFunction_0-output-returnFalse-string|number|boolean|json|array", "name": "returnFalse", "label": "False", "type": "string | number | boolean | json | array"}], "default": "returnTrue"}], "outputs": {"output": "returnTrue"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1217.9173937613534, "y": -515.3650041424041}, "dragging": false}, {"width": 300, "height": 779, "id": "promptTemplate_2", "position": {"x": 1577.4729260684187, "y": 887.7668360114285}, "type": "customNode", "data": {"id": "promptTemplate_2", "label": "Prompt Template", "version": 1, "name": "promptTemplate", "type": "PromptTemplate", "baseClasses": ["PromptTemplate", "BaseStringPromptTemplate", "BasePromptTemplate", "Runnable"], "category": "Prompts", "description": "Schema to represent a basic prompt for an LLM", "inputParams": [{"label": "Template", "name": "template", "type": "string", "rows": 4, "placeholder": "What is a good name for a company that makes {product}?", "id": "promptTemplate_2-input-template-string"}, {"label": "Format Prompt Values", "name": "promptValues", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "promptTemplate_2-input-promptValues-json"}], "inputAnchors": [], "inputs": {"template": "Politely say \"I'm not able to answer query\"", "promptValues": "{\"schema\":\"{{setVariable_0.data.instance}}\",\"question\":\"{{question}}\"}"}, "outputAnchors": [{"id": "promptTemplate_2-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable", "name": "promptTemplate", "label": "PromptTemplate", "type": "PromptTemplate | BaseStringPromptTemplate | BasePromptTemplate | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1577.4729260684187, "y": 887.7668360114285}, "dragging": false}, {"width": 300, "height": 506, "id": "llmChain_2", "position": {"x": 1942.2473639184586, "y": 534.2501352750406}, "type": "customNode", "data": {"id": "llmChain_2", "label": "LLM Chain", "version": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON>", "BaseChain", "Runnable"], "category": "Chains", "description": "Chain to run queries against LLMs", "inputParams": [{"label": "Chain Name", "name": "chainName", "type": "string", "placeholder": "Name Your Chain", "optional": true, "id": "llmChain_2-input-chainName-string"}], "inputAnchors": [{"label": "Language Model", "name": "model", "type": "BaseLanguageModel", "id": "llmChain_2-input-model-BaseLanguageModel"}, {"label": "Prompt", "name": "prompt", "type": "BasePromptTemplate", "id": "llmChain_2-input-prompt-BasePromptTemplate"}, {"label": "Output Parser", "name": "outputParser", "type": "BaseLLMOutputParser", "optional": true, "id": "llmChain_2-input-outputParser-BaseLLMOutputParser"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "llmChain_2-input-inputModeration-Moderation"}], "inputs": {"model": "{{chatOpenAI_2.data.instance}}", "prompt": "{{promptTemplate_2.data.instance}}", "outputParser": "", "inputModeration": "", "chainName": "Fallback Chain"}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "llmChain_2-output-llmChain-LLMChain|BaseChain|Runnable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "LLM Chain", "type": "LLMChain | BaseChain | Runnable"}, {"id": "llmChain_2-output-outputPrediction-string|json", "name": "outputPrediction", "label": "Output Prediction", "type": "string | json"}], "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "outputs": {"output": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1942.2473639184586, "y": 534.2501352750406}, "dragging": false}, {"id": "chatOpenAI_1", "position": {"x": 375.16318421173054, "y": -645.2584301535801}, "type": "customNode", "data": {"id": "chatOpenAI_1", "label": "ChatOpenAI", "version": 5, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_1-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "options", "options": [{"label": "gpt-4", "name": "gpt-4"}, {"label": "gpt-4-turbo-preview", "name": "gpt-4-turbo-preview"}, {"label": "gpt-4-0125-preview", "name": "gpt-4-0125-preview"}, {"label": "gpt-4-1106-preview", "name": "gpt-4-1106-preview"}, {"label": "gpt-4-1106-vision-preview", "name": "gpt-4-1106-vision-preview"}, {"label": "gpt-4-vision-preview", "name": "gpt-4-vision-preview"}, {"label": "gpt-4-0613", "name": "gpt-4-0613"}, {"label": "gpt-4-32k", "name": "gpt-4-32k"}, {"label": "gpt-4-32k-0613", "name": "gpt-4-32k-0613"}, {"label": "gpt-3.5-turbo", "name": "gpt-3.5-turbo"}, {"label": "gpt-3.5-turbo-0125", "name": "gpt-3.5-turbo-0125"}, {"label": "gpt-3.5-turbo-1106", "name": "gpt-3.5-turbo-1106"}, {"label": "gpt-3.5-turbo-0613", "name": "gpt-3.5-turbo-0613"}, {"label": "gpt-3.5-turbo-16k", "name": "gpt-3.5-turbo-16k"}, {"label": "gpt-3.5-turbo-16k-0613", "name": "gpt-3.5-turbo-16k-0613"}], "default": "gpt-3.5-turbo", "optional": true, "id": "chatOpenAI_1-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_1-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_1-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_1-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_1-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-3.5-turbo", "temperature": "0", "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": "", "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_1-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 670, "selected": false, "positionAbsolute": {"x": 375.16318421173054, "y": -645.2584301535801}, "dragging": false}, {"id": "chatOpenAI_3", "position": {"x": 1948.9511168108475, "y": -796.7149375857242}, "type": "customNode", "data": {"id": "chatOpenAI_3", "label": "ChatOpenAI", "version": 5, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_3-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "options", "options": [{"label": "gpt-4", "name": "gpt-4"}, {"label": "gpt-4-turbo-preview", "name": "gpt-4-turbo-preview"}, {"label": "gpt-4-0125-preview", "name": "gpt-4-0125-preview"}, {"label": "gpt-4-1106-preview", "name": "gpt-4-1106-preview"}, {"label": "gpt-4-1106-vision-preview", "name": "gpt-4-1106-vision-preview"}, {"label": "gpt-4-vision-preview", "name": "gpt-4-vision-preview"}, {"label": "gpt-4-0613", "name": "gpt-4-0613"}, {"label": "gpt-4-32k", "name": "gpt-4-32k"}, {"label": "gpt-4-32k-0613", "name": "gpt-4-32k-0613"}, {"label": "gpt-3.5-turbo", "name": "gpt-3.5-turbo"}, {"label": "gpt-3.5-turbo-0125", "name": "gpt-3.5-turbo-0125"}, {"label": "gpt-3.5-turbo-1106", "name": "gpt-3.5-turbo-1106"}, {"label": "gpt-3.5-turbo-0613", "name": "gpt-3.5-turbo-0613"}, {"label": "gpt-3.5-turbo-16k", "name": "gpt-3.5-turbo-16k"}, {"label": "gpt-3.5-turbo-16k-0613", "name": "gpt-3.5-turbo-16k-0613"}], "default": "gpt-3.5-turbo", "optional": true, "id": "chatOpenAI_3-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_3-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_3-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_3-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_3-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_3-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_3-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_3-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_3-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_3-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_3-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_3-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-3.5-turbo", "temperature": 0.9, "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": "", "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_3-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 670, "selected": false, "positionAbsolute": {"x": 1948.9511168108475, "y": -796.7149375857242}, "dragging": false}, {"id": "chatOpenAI_2", "position": {"x": 1566.0508325767967, "y": 194.95875577740696}, "type": "customNode", "data": {"id": "chatOpenAI_2", "label": "ChatOpenAI", "version": 5, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_2-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "options", "options": [{"label": "gpt-4", "name": "gpt-4"}, {"label": "gpt-4-turbo-preview", "name": "gpt-4-turbo-preview"}, {"label": "gpt-4-0125-preview", "name": "gpt-4-0125-preview"}, {"label": "gpt-4-1106-preview", "name": "gpt-4-1106-preview"}, {"label": "gpt-4-1106-vision-preview", "name": "gpt-4-1106-vision-preview"}, {"label": "gpt-4-vision-preview", "name": "gpt-4-vision-preview"}, {"label": "gpt-4-0613", "name": "gpt-4-0613"}, {"label": "gpt-4-32k", "name": "gpt-4-32k"}, {"label": "gpt-4-32k-0613", "name": "gpt-4-32k-0613"}, {"label": "gpt-3.5-turbo", "name": "gpt-3.5-turbo"}, {"label": "gpt-3.5-turbo-0125", "name": "gpt-3.5-turbo-0125"}, {"label": "gpt-3.5-turbo-1106", "name": "gpt-3.5-turbo-1106"}, {"label": "gpt-3.5-turbo-0613", "name": "gpt-3.5-turbo-0613"}, {"label": "gpt-3.5-turbo-16k", "name": "gpt-3.5-turbo-16k"}, {"label": "gpt-3.5-turbo-16k-0613", "name": "gpt-3.5-turbo-16k-0613"}], "default": "gpt-3.5-turbo", "optional": true, "id": "chatOpenAI_2-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_2-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_2-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_2-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_2-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-3.5-turbo", "temperature": 0.9, "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": "", "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_2-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 670, "selected": false, "positionAbsolute": {"x": 1566.0508325767967, "y": 194.95875577740696}, "dragging": false}], "edges": [{"source": "promptTemplate_0", "sourceHandle": "promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable", "target": "llmChain_0", "targetHandle": "llmChain_0-input-prompt-BasePromptTemplate", "type": "buttonedge", "id": "promptTemplate_0-promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable-llmChain_0-llmChain_0-input-prompt-BasePromptTemplate", "data": {"label": ""}}, {"source": "customFunction_1", "sourceHandle": "customFunction_1-output-output-string|number|boolean|json|array", "target": "promptTemplate_1", "targetHandle": "promptTemplate_1-input-promptValues-json", "type": "buttonedge", "id": "customFunction_1-customFunction_1-output-output-string|number|boolean|json|array-promptTemplate_1-promptTemplate_1-input-promptValues-json", "data": {"label": ""}}, {"source": "promptTemplate_1", "sourceHandle": "promptTemplate_1-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable", "target": "llmChain_1", "targetHandle": "llmChain_1-input-prompt-BasePromptTemplate", "type": "buttonedge", "id": "promptTemplate_1-promptTemplate_1-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable-llmChain_1-llmChain_1-input-prompt-BasePromptTemplate", "data": {"label": ""}}, {"source": "llmChain_0", "sourceHandle": "llmChain_0-output-outputPrediction-string|json", "target": "ifElseFunction_0", "targetHandle": "ifElseFunction_0-input-functionInputVariables-json", "type": "buttonedge", "id": "llmChain_0-llmChain_0-output-outputPrediction-string|json-ifElseFunction_0-ifElseFunction_0-input-functionInputVariables-json"}, {"source": "ifElseFunction_0", "sourceHandle": "ifElseFunction_0-output-returnFalse-string|number|boolean|json|array", "target": "promptTemplate_2", "targetHandle": "promptTemplate_2-input-promptValues-json", "type": "buttonedge", "id": "ifElseFunction_0-ifElseFunction_0-output-returnFalse-string|number|boolean|json|array-promptTemplate_2-promptTemplate_2-input-promptValues-json"}, {"source": "promptTemplate_2", "sourceHandle": "promptTemplate_2-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable", "target": "llmChain_2", "targetHandle": "llmChain_2-input-prompt-BasePromptTemplate", "type": "buttonedge", "id": "promptTemplate_2-promptTemplate_2-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable-llmChain_2-llmChain_2-input-prompt-BasePromptTemplate"}, {"source": "customFunction_2", "sourceHandle": "customFunction_2-output-output-string|number|boolean|json|array", "target": "promptTemplate_0", "targetHandle": "promptTemplate_0-input-promptValues-json", "type": "buttonedge", "id": "customFunction_2-customFunction_2-output-output-string|number|boolean|json|array-promptTemplate_0-promptTemplate_0-input-promptValues-json"}, {"source": "ifElseFunction_0", "sourceHandle": "ifElseFunction_0-output-returnTrue-string|number|boolean|json|array", "target": "customFunction_1", "targetHandle": "customFunction_1-input-functionInputVariables-json", "type": "buttonedge", "id": "ifElseFunction_0-ifElseFunction_0-output-returnTrue-string|number|boolean|json|array-customFunction_1-customFunction_1-input-functionInputVariables-json"}, {"source": "chatOpenAI_1", "sourceHandle": "chatOpenAI_1-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "llmChain_0", "targetHandle": "llmChain_0-input-model-BaseLanguageModel", "type": "buttonedge", "id": "chatOpenAI_1-chatOpenAI_1-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-llmChain_0-llmChain_0-input-model-BaseLanguageModel"}, {"source": "chatOpenAI_3", "sourceHandle": "chatOpenAI_3-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "llmChain_1", "targetHandle": "llmChain_1-input-model-BaseLanguageModel", "type": "buttonedge", "id": "chatOpenAI_3-chatOpenAI_3-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-llmChain_1-llmChain_1-input-model-BaseLanguageModel"}, {"source": "chatOpenAI_2", "sourceHandle": "chatOpenAI_2-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "llmChain_2", "targetHandle": "llmChain_2-input-model-BaseLanguageModel", "type": "buttonedge", "id": "chatOpenAI_2-chatOpenAI_2-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-llmChain_2-llmChain_2-input-model-BaseLanguageModel"}]}