{"nodes": [{"width": 300, "height": 554, "id": "pinecone_0", "position": {"x": 416.0885364955418, "y": -74.64623359488957}, "type": "customNode", "data": {"id": "pinecone_0", "label": "Pinecone", "version": 2, "name": "pinecone", "type": "Pinecone", "baseClasses": ["Pinecone", "VectorStoreRetriever", "BaseRetriever"], "category": "Vector Stores", "description": "Upsert embedded data and perform similarity or mmr search using Pinecone, a leading fully managed hosted vector database", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["pineconeApi"], "id": "pinecone_0-input-credential-credential"}, {"label": "Pinecone Index", "name": "pineconeIndex", "type": "string", "id": "pinecone_0-input-pineconeIndex-string"}, {"label": "Pinecone Namespace", "name": "pineconeNamespace", "type": "string", "placeholder": "my-first-namespace", "additionalParams": true, "optional": true, "id": "pinecone_0-input-pineconeNamespace-string"}, {"label": "Pinecone Metadata Filter", "name": "pineconeMetadataFilter", "type": "json", "optional": true, "additionalParams": true, "id": "pinecone_0-input-pineconeMetadataFilter-json"}, {"label": "Top K", "name": "topK", "description": "Number of top results to fetch. De<PERSON><PERSON> to 4", "placeholder": "4", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_0-input-topK-number"}, {"label": "Search Type", "name": "searchType", "type": "options", "default": "similarity", "options": [{"label": "Similarity", "name": "similarity"}, {"label": "<PERSON>ginal Relevance", "name": "mmr"}], "additionalParams": true, "optional": true, "id": "pinecone_0-input-searchType-options"}, {"label": "Fetch K (for MMR Search)", "name": "fetchK", "description": "Number of initial documents to fetch for MMR reranking. Default to 20. Used only when the search type is MMR", "placeholder": "20", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_0-input-fetchK-number"}, {"label": "Lambda (for MMR Search)", "name": "lambda", "description": "Number between 0 and 1 that determines the degree of diversity among the results, where 0 corresponds to maximum diversity and 1 to minimum diversity. Used only when the search type is MMR", "placeholder": "0.5", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_0-input-lambda-number"}], "inputAnchors": [{"label": "Document", "name": "document", "type": "Document", "list": true, "optional": true, "id": "pinecone_0-input-document-Document"}, {"label": "Embeddings", "name": "embeddings", "type": "Embeddings", "id": "pinecone_0-input-embeddings-Embeddings"}], "inputs": {"document": "", "embeddings": "{{openAIEmbeddings_0.data.instance}}", "pineconeIndex": "newindex", "pineconeNamespace": "pinecone-form10k", "pineconeMetadataFilter": "{\"source\":\"apple\"}", "topK": "", "searchType": "similarity", "fetchK": "", "lambda": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "pinecone_0-output-retriever-Pinecone|VectorStoreRetriever|BaseRetriever", "name": "retriever", "label": "Pinecone Retriever", "type": "Pinecone | VectorStoreRetriever | BaseRetriever"}, {"id": "pinecone_0-output-vectorStore-Pinecone|VectorStore", "name": "vectorStore", "label": "Pinecone Vector Store", "type": "Pinecone | VectorStore"}], "default": "retriever"}], "outputs": {"output": "retriever"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 416.0885364955418, "y": -74.64623359488957}, "dragging": false}, {"width": 300, "height": 423, "id": "openAIEmbeddings_0", "position": {"x": 54.119166092646566, "y": -20.12821243199312}, "type": "customNode", "data": {"id": "openAIEmbeddings_0", "label": "OpenAI Embeddings", "version": 2, "name": "openAIEmbeddings", "type": "OpenAIEmbeddings", "baseClasses": ["OpenAIEmbeddings", "Embeddings"], "category": "Embeddings", "description": "OpenAI API to generate embeddings for a given text", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "openAIEmbeddings_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "options", "options": [{"label": "text-embedding-3-large", "name": "text-embedding-3-large"}, {"label": "text-embedding-3-small", "name": "text-embedding-3-small"}, {"label": "text-embedding-ada-002", "name": "text-embedding-ada-002"}], "default": "text-embedding-ada-002", "optional": true, "id": "openAIEmbeddings_0-input-modelName-options"}, {"label": "Strip New Lines", "name": "stripNewLines", "type": "boolean", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-stripNewLines-boolean"}, {"label": "<PERSON><PERSON> Si<PERSON>", "name": "batchSize", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-batchSize-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-basepath-string"}], "inputAnchors": [], "inputs": {"modelName": "text-embedding-ada-002", "stripNewLines": "", "batchSize": "", "timeout": "", "basepath": ""}, "outputAnchors": [{"id": "openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "name": "openAIEmbeddings", "label": "OpenAIEmbeddings", "type": "OpenAIEmbeddings | Embeddings"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 54.119166092646566, "y": -20.12821243199312}, "dragging": false}, {"width": 300, "height": 554, "id": "pinecone_1", "position": {"x": 428.41115568995156, "y": 549.0169795435812}, "type": "customNode", "data": {"id": "pinecone_1", "label": "Pinecone", "version": 2, "name": "pinecone", "type": "Pinecone", "baseClasses": ["Pinecone", "VectorStoreRetriever", "BaseRetriever"], "category": "Vector Stores", "description": "Upsert embedded data and perform similarity or mmr search using Pinecone, a leading fully managed hosted vector database", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["pineconeApi"], "id": "pinecone_1-input-credential-credential"}, {"label": "Pinecone Index", "name": "pineconeIndex", "type": "string", "id": "pinecone_1-input-pineconeIndex-string"}, {"label": "Pinecone Namespace", "name": "pineconeNamespace", "type": "string", "placeholder": "my-first-namespace", "additionalParams": true, "optional": true, "id": "pinecone_1-input-pineconeNamespace-string"}, {"label": "Pinecone Metadata Filter", "name": "pineconeMetadataFilter", "type": "json", "optional": true, "additionalParams": true, "id": "pinecone_1-input-pineconeMetadataFilter-json"}, {"label": "Top K", "name": "topK", "description": "Number of top results to fetch. De<PERSON><PERSON> to 4", "placeholder": "4", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_1-input-topK-number"}, {"label": "Search Type", "name": "searchType", "type": "options", "default": "similarity", "options": [{"label": "Similarity", "name": "similarity"}, {"label": "<PERSON>ginal Relevance", "name": "mmr"}], "additionalParams": true, "optional": true, "id": "pinecone_1-input-searchType-options"}, {"label": "Fetch K (for MMR Search)", "name": "fetchK", "description": "Number of initial documents to fetch for MMR reranking. Default to 20. Used only when the search type is MMR", "placeholder": "20", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_1-input-fetchK-number"}, {"label": "Lambda (for MMR Search)", "name": "lambda", "description": "Number between 0 and 1 that determines the degree of diversity among the results, where 0 corresponds to maximum diversity and 1 to minimum diversity. Used only when the search type is MMR", "placeholder": "0.5", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_1-input-lambda-number"}], "inputAnchors": [{"label": "Document", "name": "document", "type": "Document", "list": true, "optional": true, "id": "pinecone_1-input-document-Document"}, {"label": "Embeddings", "name": "embeddings", "type": "Embeddings", "id": "pinecone_1-input-embeddings-Embeddings"}], "inputs": {"document": "", "embeddings": "{{openAIEmbeddings_1.data.instance}}", "pineconeIndex": "newindex", "pineconeNamespace": "pinecone-form10k-2", "pineconeMetadataFilter": "{\"source\":\"tesla\"}", "topK": "", "searchType": "similarity", "fetchK": "", "lambda": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "pinecone_1-output-retriever-Pinecone|VectorStoreRetriever|BaseRetriever", "name": "retriever", "label": "Pinecone Retriever", "type": "Pinecone | VectorStoreRetriever | BaseRetriever"}, {"id": "pinecone_1-output-vectorStore-Pinecone|VectorStore", "name": "vectorStore", "label": "Pinecone Vector Store", "type": "Pinecone | VectorStore"}], "default": "retriever"}], "outputs": {"output": "retriever"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 428.41115568995156, "y": 549.0169795435812}, "dragging": false}, {"width": 300, "height": 423, "id": "openAIEmbeddings_1", "position": {"x": 58.45057557109914, "y": 575.7733202609951}, "type": "customNode", "data": {"id": "openAIEmbeddings_1", "label": "OpenAI Embeddings", "version": 2, "name": "openAIEmbeddings", "type": "OpenAIEmbeddings", "baseClasses": ["OpenAIEmbeddings", "Embeddings"], "category": "Embeddings", "description": "OpenAI API to generate embeddings for a given text", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "openAIEmbeddings_1-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "options", "options": [{"label": "text-embedding-3-large", "name": "text-embedding-3-large"}, {"label": "text-embedding-3-small", "name": "text-embedding-3-small"}, {"label": "text-embedding-ada-002", "name": "text-embedding-ada-002"}], "default": "text-embedding-ada-002", "optional": true, "id": "openAIEmbeddings_1-input-modelName-options"}, {"label": "Strip New Lines", "name": "stripNewLines", "type": "boolean", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_1-input-stripNewLines-boolean"}, {"label": "<PERSON><PERSON> Si<PERSON>", "name": "batchSize", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_1-input-batchSize-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_1-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_1-input-basepath-string"}], "inputAnchors": [], "inputs": {"modelName": "text-embedding-ada-002", "stripNewLines": "", "batchSize": "", "timeout": "", "basepath": ""}, "outputAnchors": [{"id": "openAIEmbeddings_1-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "name": "openAIEmbeddings", "label": "OpenAIEmbeddings", "type": "OpenAIEmbeddings | Embeddings"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 58.45057557109914, "y": 575.7733202609951}, "dragging": false}, {"width": 300, "height": 376, "id": "bufferMemory_0", "position": {"x": 825.5960565466753, "y": 1212.2401709995304}, "type": "customNode", "data": {"id": "bufferMemory_0", "label": "Buffer Memory", "version": 1, "name": "bufferMemory", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BaseChatMemory", "BaseMemory"], "category": "Memory", "description": "Remembers previous conversational back and forths directly", "inputParams": [{"label": "Memory Key", "name": "<PERSON><PERSON><PERSON>", "type": "string", "default": "chat_history", "id": "bufferMemory_0-input-memoryKey-string"}, {"label": "Input Key", "name": "inputKey", "type": "string", "default": "input", "id": "bufferMemory_0-input-inputKey-string"}], "inputAnchors": [], "inputs": {"memoryKey": "chat_history", "inputKey": "input"}, "outputAnchors": [{"id": "bufferMemory_0-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory", "name": "bufferMemory", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "BufferMemory | BaseChatMemory | BaseMemory"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 825.5960565466753, "y": 1212.2401709995304}, "dragging": false}, {"width": 300, "height": 382, "id": "openAIFunctionAgent_0", "position": {"x": 1461.716457981219, "y": 547.2159602910168}, "type": "customNode", "data": {"id": "openAIFunctionAgent_0", "label": "OpenAI Function Agent", "version": 3, "name": "openAIFunctionAgent", "type": "AgentExecutor", "baseClasses": ["AgentExecutor", "BaseChain", "Runnable"], "category": "Agents", "description": "An agent that uses Function Calling to pick the tool and args to call", "inputParams": [{"label": "System Message", "name": "systemMessage", "type": "string", "rows": 4, "optional": true, "additionalParams": true, "id": "openAIFunctionAgent_0-input-systemMessage-string"}], "inputAnchors": [{"label": "Allowed Tools", "name": "tools", "type": "Tool", "list": true, "id": "openAIFunctionAgent_0-input-tools-Tool"}, {"label": "Memory", "name": "memory", "type": "BaseChatMemory", "id": "openAIFunctionAgent_0-input-memory-BaseChatMemory"}, {"label": "OpenAI/Azure Chat Model", "name": "model", "type": "BaseChatModel", "id": "openAIFunctionAgent_0-input-model-BaseChatModel"}], "inputs": {"tools": ["{{retrieverTool_1.data.instance}}", "{{retrieverTool_2.data.instance}}"], "memory": "{{bufferMemory_0.data.instance}}", "model": "{{chatOpenAI_0.data.instance}}", "systemMessage": "You are an expert financial analyst that always answers questions with the most relevant information using the tools at your disposal.\nThese tools have information regarding companies that the user has expressed interest in.\nHere are some guidelines that you must follow:\n* For financial questions, you must use the tools to find the answer and then write a response.\n* Even if it seems like your tools won't be able to answer the question, you must still use them to find the most relevant information and insights. Not using them will appear as if you are not doing your job.\n* You may assume that the users financial questions are related to the documents they've selected.\n* For any user message that isn't related to financial analysis, respectfully decline to respond and suggest that the user ask a relevant question.\n* If your tools are unable to find an answer, you should say that you haven't found an answer but still relay any useful information the tools found.\n* Dont ask clarifying questions, just return answer.\n\nThe tools at your disposal have access to the following SEC documents that the user has selected to discuss with you:\n- Apple Inc (APPL) FORM 10K 2022\n- Tesla Inc (TSLA) FORM 10K 2022\n\nThe current date is: 2024-01-28"}, "outputAnchors": [{"id": "openAIFunctionAgent_0-output-openAIFunctionAgent-AgentExecutor|BaseChain|Runnable", "name": "openAIFunctionAgent", "label": "AgentExecutor", "type": "AgentExecutor | BaseChain | Runnable"}], "outputs": {}, "selected": false}, "positionAbsolute": {"x": 1461.716457981219, "y": 547.2159602910168}, "selected": false, "dragging": false}, {"width": 300, "height": 781, "id": "retrieverTool_2", "position": {"x": 798.3128281367018, "y": -151.77659673435184}, "type": "customNode", "data": {"id": "retrieverTool_2", "label": "Retriever Tool", "version": 2, "name": "retrieverTool", "type": "RetrieverTool", "baseClasses": ["RetrieverTool", "DynamicTool", "Tool", "StructuredTool", "Runnable"], "category": "Tools", "description": "Use a retriever as allowed tool for agent", "inputParams": [{"label": "Retriever Name", "name": "name", "type": "string", "placeholder": "search_state_of_union", "id": "retrieverTool_2-input-name-string"}, {"label": "Retriever Description", "name": "description", "type": "string", "description": "When should agent uses to retrieve documents", "rows": 3, "placeholder": "Searches and returns documents regarding the state-of-the-union.", "id": "retrieverTool_2-input-description-string"}, {"label": "Return Source Documents", "name": "returnSourceDocuments", "type": "boolean", "optional": true, "id": "retrieverTool_2-input-returnSourceDocuments-boolean"}], "inputAnchors": [{"label": "Retriever", "name": "retriever", "type": "BaseRetriever", "id": "retrieverTool_2-input-retriever-BaseRetriever"}], "inputs": {"name": "search_apple", "description": "Use this function to answer user questions about Apple Inc (APPL). It contains a SEC Form 10K filing describing the financials of Apple Inc (APPL) for the 2022 time period.", "retriever": "{{pinecone_0.data.instance}}", "returnSourceDocuments": true}, "outputAnchors": [{"id": "retrieverTool_2-output-retrieverTool-RetrieverTool|DynamicTool|Tool|StructuredTool|Runnable", "name": "retrieverTool", "label": "RetrieverTool", "type": "RetrieverTool | DynamicTool | Tool | StructuredTool | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 798.3128281367018, "y": -151.77659673435184}, "dragging": false}, {"width": 300, "height": 781, "id": "retrieverTool_1", "position": {"x": 805.1192462354428, "y": 479.4961512574057}, "type": "customNode", "data": {"id": "retrieverTool_1", "label": "Retriever Tool", "version": 2, "name": "retrieverTool", "type": "RetrieverTool", "baseClasses": ["RetrieverTool", "DynamicTool", "Tool", "StructuredTool", "Runnable"], "category": "Tools", "description": "Use a retriever as allowed tool for agent", "inputParams": [{"label": "Retriever Name", "name": "name", "type": "string", "placeholder": "search_state_of_union", "id": "retrieverTool_1-input-name-string"}, {"label": "Retriever Description", "name": "description", "type": "string", "description": "When should agent uses to retrieve documents", "rows": 3, "placeholder": "Searches and returns documents regarding the state-of-the-union.", "id": "retrieverTool_1-input-description-string"}, {"label": "Return Source Documents", "name": "returnSourceDocuments", "type": "boolean", "optional": true, "id": "retrieverTool_1-input-returnSourceDocuments-boolean"}], "inputAnchors": [{"label": "Retriever", "name": "retriever", "type": "BaseRetriever", "id": "retrieverTool_1-input-retriever-BaseRetriever"}], "inputs": {"name": "search_tsla", "description": "Use this function to answer user questions about Tesla Inc (TSLA). It contains a SEC Form 10K filing describing the financials of Tesla Inc (TSLA) for the 2022 time period.", "retriever": "{{pinecone_1.data.instance}}", "returnSourceDocuments": true}, "outputAnchors": [{"id": "retrieverTool_1-output-retrieverTool-RetrieverTool|DynamicTool|Tool|StructuredTool|Runnable", "name": "retrieverTool", "label": "RetrieverTool", "type": "RetrieverTool | DynamicTool | Tool | StructuredTool | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 805.1192462354428, "y": 479.4961512574057}, "dragging": false}, {"id": "chatOpenAI_0", "position": {"x": 813.5701421468654, "y": 1658.1569949989084}, "type": "customNode", "data": {"id": "chatOpenAI_0", "label": "ChatOpenAI", "version": 5, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "options", "options": [{"label": "gpt-4", "name": "gpt-4"}, {"label": "gpt-4-turbo-preview", "name": "gpt-4-turbo-preview"}, {"label": "gpt-4-0125-preview", "name": "gpt-4-0125-preview"}, {"label": "gpt-4-1106-preview", "name": "gpt-4-1106-preview"}, {"label": "gpt-4-1106-vision-preview", "name": "gpt-4-1106-vision-preview"}, {"label": "gpt-4-vision-preview", "name": "gpt-4-vision-preview"}, {"label": "gpt-4-0613", "name": "gpt-4-0613"}, {"label": "gpt-4-32k", "name": "gpt-4-32k"}, {"label": "gpt-4-32k-0613", "name": "gpt-4-32k-0613"}, {"label": "gpt-3.5-turbo", "name": "gpt-3.5-turbo"}, {"label": "gpt-3.5-turbo-0125", "name": "gpt-3.5-turbo-0125"}, {"label": "gpt-3.5-turbo-1106", "name": "gpt-3.5-turbo-1106"}, {"label": "gpt-3.5-turbo-0613", "name": "gpt-3.5-turbo-0613"}, {"label": "gpt-3.5-turbo-16k", "name": "gpt-3.5-turbo-16k"}, {"label": "gpt-3.5-turbo-16k-0613", "name": "gpt-3.5-turbo-16k-0613"}], "default": "gpt-3.5-turbo", "optional": true, "id": "chatOpenAI_0-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_0-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_0-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_0-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_0-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-3.5-turbo", "temperature": 0.9, "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": "", "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 670, "selected": false, "positionAbsolute": {"x": 813.5701421468654, "y": 1658.1569949989084}, "dragging": false}], "edges": [{"source": "openAIEmbeddings_0", "sourceHandle": "openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "target": "pinecone_0", "targetHandle": "pinecone_0-input-embeddings-Embeddings", "type": "buttonedge", "id": "openAIEmbeddings_0-openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings-pinecone_0-pinecone_0-input-embeddings-Embeddings"}, {"source": "openAIEmbeddings_1", "sourceHandle": "openAIEmbeddings_1-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "target": "pinecone_1", "targetHandle": "pinecone_1-input-embeddings-Embeddings", "type": "buttonedge", "id": "openAIEmbeddings_1-openAIEmbeddings_1-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings-pinecone_1-pinecone_1-input-embeddings-Embeddings"}, {"source": "bufferMemory_0", "sourceHandle": "bufferMemory_0-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory", "target": "openAIFunctionAgent_0", "targetHandle": "openAIFunctionAgent_0-input-memory-BaseChatMemory", "type": "buttonedge", "id": "bufferMemory_0-bufferMemory_0-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory-openAIFunctionAgent_0-openAIFunctionAgent_0-input-memory-BaseChatMemory"}, {"source": "pinecone_0", "sourceHandle": "pinecone_0-output-retriever-Pinecone|VectorStoreRetriever|BaseRetriever", "target": "retrieverTool_2", "targetHandle": "retrieverTool_2-input-retriever-BaseRetriever", "type": "buttonedge", "id": "pinecone_0-pinecone_0-output-retriever-<PERSON>con<PERSON>|VectorStoreRetriever|BaseRetriever-retrieverTool_2-retrieverTool_2-input-retriever-BaseRetriever"}, {"source": "pinecone_1", "sourceHandle": "pinecone_1-output-retriever-Pinecone|VectorStoreRetriever|BaseRetriever", "target": "retrieverTool_1", "targetHandle": "retrieverTool_1-input-retriever-BaseRetriever", "type": "buttonedge", "id": "pinecone_1-pinecone_1-output-retriever-<PERSON>con<PERSON>|VectorStoreRetriever|BaseRetriever-retrieverTool_1-retrieverTool_1-input-retriever-BaseRetriever"}, {"source": "retrieverTool_1", "sourceHandle": "retrieverTool_1-output-retrieverTool-RetrieverTool|DynamicTool|Tool|StructuredTool|Runnable", "target": "openAIFunctionAgent_0", "targetHandle": "openAIFunctionAgent_0-input-tools-Tool", "type": "buttonedge", "id": "retrieverTool_1-retrieverTool_1-output-retrieverTool-RetrieverTool|DynamicTool|Tool|StructuredTool|Runnable-openAIFunctionAgent_0-openAIFunctionAgent_0-input-tools-Tool"}, {"source": "retrieverTool_2", "sourceHandle": "retrieverTool_2-output-retrieverTool-RetrieverTool|DynamicTool|Tool|StructuredTool|Runnable", "target": "openAIFunctionAgent_0", "targetHandle": "openAIFunctionAgent_0-input-tools-Tool", "type": "buttonedge", "id": "retrieverTool_2-retrieverTool_2-output-retrieverTool-RetrieverTool|DynamicTool|Tool|StructuredTool|Runnable-openAIFunctionAgent_0-openAIFunctionAgent_0-input-tools-Tool"}, {"source": "chatOpenAI_0", "sourceHandle": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "openAIFunctionAgent_0", "targetHandle": "openAIFunctionAgent_0-input-model-BaseChatModel", "type": "buttonedge", "id": "chatOpenAI_0-chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-openAIFunctionAgent_0-openAIFunctionAgent_0-input-model-BaseChatModel"}]}