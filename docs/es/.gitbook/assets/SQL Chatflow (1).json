{"nodes": [{"width": 300, "height": 511, "id": "promptTemplate_0", "position": {"x": 384.4880563109088, "y": 253.48974179902635}, "type": "customNode", "data": {"id": "promptTemplate_0", "label": "Prompt Template", "version": 1, "name": "promptTemplate", "type": "PromptTemplate", "baseClasses": ["PromptTemplate", "BaseStringPromptTemplate", "BasePromptTemplate", "Runnable"], "category": "Prompts", "description": "Schema to represent a basic prompt for an LLM", "inputParams": [{"label": "Template", "name": "template", "type": "string", "rows": 4, "placeholder": "What is a good name for a company that makes {product}?", "id": "promptTemplate_0-input-template-string"}, {"label": "Format Prompt Values", "name": "promptValues", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "promptTemplate_0-input-promptValues-json"}], "inputAnchors": [], "inputs": {"template": "You are a MySQL expert. Given an input question, create a syntactically correct MySQL query to run.\nUnless otherwise specified, do not return more than {topK} rows.\n\nHere is the relevant table info:\n{schema}\n\nBelow are a number of examples of questions and their corresponding SQL queries.\n\nUser input: List all artists.\nSQL Query: SELECT * FROM Artist;\n\nUser input: Find all albums for the artist 'AC/DC'.\nSQL Query: SELECT * FROM Album WHERE ArtistId = (SELECT ArtistId FROM Artist WHERE Name = 'AC/DC');\n\nUser input: List all tracks in the 'Rock' genre.\nSQL Query: SELECT * FROM Track WHERE GenreId = (SELECT GenreId FROM Genre WHERE Name = 'Rock');\n\nUser input: Find the total duration of all tracks.\nSQL Query: SELECT SUM(Milliseconds) FROM Track;\n\nUser input: List all customers from Canada.\nSQL Query: SELECT * FROM Customer WHERE Country = 'Canada';\n\nUser input: {question}\nSQL query:", "promptValues": "{\"schema\":\"{{customFunction_2.data.instance}}\",\"question\":\"{{question}}\",\"topK\":3}"}, "outputAnchors": [{"id": "promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable", "name": "promptTemplate", "label": "PromptTemplate", "type": "PromptTemplate | BaseStringPromptTemplate | BasePromptTemplate | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 384.4880563109088, "y": 253.48974179902635}, "dragging": false}, {"width": 300, "height": 507, "id": "llmChain_0", "position": {"x": 770.4559230968546, "y": -127.11351409346554}, "type": "customNode", "data": {"id": "llmChain_0", "label": "LLM Chain", "version": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON>", "BaseChain", "Runnable"], "category": "Chains", "description": "Chain to run queries against LLMs", "inputParams": [{"label": "Chain Name", "name": "chainName", "type": "string", "placeholder": "Name Your Chain", "optional": true, "id": "llmChain_0-input-chainName-string"}], "inputAnchors": [{"label": "Language Model", "name": "model", "type": "BaseLanguageModel", "id": "llmChain_0-input-model-BaseLanguageModel"}, {"label": "Prompt", "name": "prompt", "type": "BasePromptTemplate", "id": "llmChain_0-input-prompt-BasePromptTemplate"}, {"label": "Output Parser", "name": "outputParser", "type": "BaseLLMOutputParser", "optional": true, "id": "llmChain_0-input-outputParser-BaseLLMOutputParser"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "llmChain_0-input-inputModeration-Moderation"}], "inputs": {"model": "{{chatOpenAI_0.data.instance}}", "prompt": "{{promptTemplate_0.data.instance}}", "outputParser": "", "inputModeration": "", "chainName": "SQL Query Chain"}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "llmChain_0-output-llmChain-LLMChain|BaseChain|Runnable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "LLM Chain", "type": "LLMChain | BaseChain | Runnable"}, {"id": "llmChain_0-output-outputPrediction-string|json", "name": "outputPrediction", "label": "Output Prediction", "type": "string | json"}], "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "outputs": {"output": "outputPrediction"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 770.4559230968546, "y": -127.11351409346554}, "dragging": false}, {"width": 300, "height": 669, "id": "chatOpenAI_0", "position": {"x": 376.92707114970364, "y": -666.8088336865496}, "type": "customNode", "data": {"id": "chatOpenAI_0", "label": "ChatOpenAI", "version": 6, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_0-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_0-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_0-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_0-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_0-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-3.5-turbo-16k", "temperature": "0", "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": true, "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 376.92707114970364, "y": -666.8088336865496}, "dragging": false}, {"width": 300, "height": 669, "id": "chatOpenAI_1", "position": {"x": 2653.726672579251, "y": -665.8849139437705}, "type": "customNode", "data": {"id": "chatOpenAI_1", "label": "ChatOpenAI", "version": 6, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_1-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_1-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_1-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_1-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_1-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_1-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-3.5-turbo-16k", "temperature": "0", "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": true, "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_1-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 2653.726672579251, "y": -665.8849139437705}, "dragging": false}, {"width": 300, "height": 507, "id": "llmChain_1", "position": {"x": 3089.9937691022837, "y": -109.24001734925716}, "type": "customNode", "data": {"id": "llmChain_1", "label": "LLM Chain", "version": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON>", "BaseChain", "Runnable"], "category": "Chains", "description": "Chain to run queries against LLMs", "inputParams": [{"label": "Chain Name", "name": "chainName", "type": "string", "placeholder": "Name Your Chain", "optional": true, "id": "llmChain_1-input-chainName-string"}], "inputAnchors": [{"label": "Language Model", "name": "model", "type": "BaseLanguageModel", "id": "llmChain_1-input-model-BaseLanguageModel"}, {"label": "Prompt", "name": "prompt", "type": "BasePromptTemplate", "id": "llmChain_1-input-prompt-BasePromptTemplate"}, {"label": "Output Parser", "name": "outputParser", "type": "BaseLLMOutputParser", "optional": true, "id": "llmChain_1-input-outputParser-BaseLLMOutputParser"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "llmChain_1-input-inputModeration-Moderation"}], "inputs": {"model": "{{chatOpenAI_1.data.instance}}", "prompt": "{{promptTemplate_1.data.instance}}", "outputParser": "", "inputModeration": "", "chainName": "Final Chain"}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "llmChain_1-output-llmChain-LLMChain|BaseChain|Runnable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "LLM Chain", "type": "LLMChain | BaseChain | Runnable"}, {"id": "llmChain_1-output-outputPrediction-string|json", "name": "outputPrediction", "label": "Output Prediction", "type": "string | json"}], "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "outputs": {"output": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 3089.9937691022837, "y": -109.24001734925716}, "dragging": false}, {"width": 300, "height": 674, "id": "customFunction_2", "position": {"x": -19.95227863012829, "y": -125.50600296188355}, "type": "customNode", "data": {"id": "customFunction_2", "label": "Custom JS Function", "version": 2, "name": "customFunction", "type": "CustomFunction", "baseClasses": ["CustomFunction", "Utilities"], "tags": ["Utilities"], "category": "Utilities", "description": "Execute custom javascript function", "inputParams": [{"label": "Input Variables", "name": "functionInputVariables", "description": "Input variables can be used in the function with prefix $. For example: $var", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "customFunction_2-input-functionInputVariables-json"}, {"label": "Function Name", "name": "functionName", "type": "string", "optional": true, "placeholder": "My Function", "id": "customFunction_2-input-functionName-string"}, {"label": "Javascript Function", "name": "javascriptFunction", "type": "code", "id": "customFunction_2-input-javascriptFunction-code"}], "inputAnchors": [], "inputs": {"functionInputVariables": "", "functionName": "Get SQL Schema Prompt", "javascriptFunction": "const HOST = 'singlestore-host.com';\nconst USER = 'admin';\nconst PASSWORD = 'mypassword';\nconst DATABASE = 'mydb';\nconst TABLE = 'samples';\nconst mysql = require('mysql2/promise');\n\nlet sqlSchemaPrompt;\n\n/**\n * Ideal prompt contains schema info and examples\n * Follows best practices as specified form https://arxiv.org/abs/2204.00498\n * =========================================\n * CREATE TABLE samples (firstName varchar NOT NULL, lastName varchar)\n * SELECT * FROM samples LIMIT 3\n * firstName lastName\n * <PERSON>\n * Jack <PERSON>\n * Steven <PERSON>ici\n * =========================================\n*/\nfunction getSQLPrompt() {\n  return new Promise(async (resolve, reject) => {\n    try {\n      const singleStoreConnection = mysql.createPool({\n        host: HOST,\n        user: USER,\n        password: PASSWORD,\n        database: DATABASE,\n      });\n  \n      // Get schema info\n      const [schemaInfo] = await singleStoreConnection.execute(\n        `SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = \"${TABLE}\"`\n      );\n  \n      const createColumns = [];\n      const columnNames = [];\n  \n      for (const schemaData of schemaInfo) {\n        columnNames.push(`${schemaData['COLUMN_NAME']}`);\n        createColumns.push(`${schemaData['COLUMN_NAME']} ${schemaData['COLUMN_TYPE']} ${schemaData['IS_NULLABLE'] === 'NO' ? 'NOT NULL' : ''}`);\n      }\n  \n      const sqlCreateTableQuery = `CREATE TABLE samples (${createColumns.join(', ')})`;\n      const sqlSelectTableQuery = `SELECT * FROM samples LIMIT 3`;\n  \n      // Get first 3 rows\n      const [rows] = await singleStoreConnection.execute(\n          sqlSelectTableQuery,\n      );\n      \n      const allValues = [];\n      for (const row of rows) {\n          const rowValues = [];\n          for (const colName in row) {\n              rowValues.push(row[colName]);\n          }\n          allValues.push(rowValues.join(' '));\n      }\n  \n      sqlSchemaPrompt = sqlCreateTableQuery + '\\n' + sqlSelectTableQuery + '\\n' + columnNames.join(' ') + '\\n' + allValues.join('\\n');\n      \n      resolve();\n    } catch (e) {\n      console.error(e);\n      return reject(e);\n    }\n  });\n}\n\nasync function main() {\n    await getSQLPrompt();\n}\n\nawait main();\n\nreturn sqlSchemaPrompt;"}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "description": "", "options": [{"id": "customFunction_2-output-output-string|number|boolean|json|array", "name": "output", "label": "Output", "description": "", "type": "string | number | boolean | json | array"}, {"id": "customFunction_2-output-EndingNode-CustomFunction", "name": "EndingNode", "label": "Ending Node", "description": "", "type": "CustomFunction"}], "default": "output"}], "outputs": {"output": "output"}, "selected": false}, "selected": false, "positionAbsolute": {"x": -19.95227863012829, "y": -125.50600296188355}, "dragging": false}, {"width": 300, "height": 674, "id": "customFunction_1", "position": {"x": 1887.4670208331604, "y": -275.95340782935716}, "type": "customNode", "data": {"id": "customFunction_1", "label": "Custom JS Function", "version": 2, "name": "customFunction", "type": "CustomFunction", "baseClasses": ["CustomFunction", "Utilities"], "tags": ["Utilities"], "category": "Utilities", "description": "Execute custom javascript function", "inputParams": [{"label": "Input Variables", "name": "functionInputVariables", "description": "Input variables can be used in the function with prefix $. For example: $var", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "customFunction_1-input-functionInputVariables-json"}, {"label": "Function Name", "name": "functionName", "type": "string", "optional": true, "placeholder": "My Function", "id": "customFunction_1-input-functionName-string"}, {"label": "Javascript Function", "name": "javascriptFunction", "type": "code", "id": "customFunction_1-input-javascriptFunction-code"}], "inputAnchors": [], "inputs": {"functionInputVariables": "{\"sqlQuery\":\"{{setVariable_1.data.instance}}\"}", "functionName": "Run SQL Query", "javascriptFunction": "const HOST = 'singlestore-host.com';\nconst USER = 'admin';\nconst PASSWORD = 'mypassword';\nconst DATABASE = 'mydb';\nconst TABLE = 'samples';\nconst mysql = require('mysql2/promise');\n\nlet result;\n\nfunction getSQLResult() {\n  return new Promise(async (resolve, reject) => {\n    try {\n      const singleStoreConnection = mysql.createPool({\n        host: HOST,\n        user: USER,\n        password: PASSWORD,\n        database: DATABASE,\n      });\n     \n      const [rows] = await singleStoreConnection.execute(\n        $sqlQuery\n      );\n  \n      result = JSON.stringify(rows)\n      \n      resolve();\n    } catch (e) {\n      console.error(e);\n      return reject(e);\n    }\n  });\n}\n\nasync function main() {\n    await getSQLResult();\n}\n\nawait main();\n\nreturn result;"}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "description": "", "options": [{"id": "customFunction_1-output-output-string|number|boolean|json|array", "name": "output", "label": "Output", "description": "", "type": "string | number | boolean | json | array"}, {"id": "customFunction_1-output-EndingNode-CustomFunction", "name": "EndingNode", "label": "Ending Node", "description": "", "type": "CustomFunction"}], "default": "output"}], "outputs": {"output": "output"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1887.4670208331604, "y": -275.95340782935716}, "dragging": false}, {"width": 300, "height": 511, "id": "promptTemplate_1", "position": {"x": 2655.2632506040304, "y": 218.145615216618}, "type": "customNode", "data": {"id": "promptTemplate_1", "label": "Prompt Template", "version": 1, "name": "promptTemplate", "type": "PromptTemplate", "baseClasses": ["PromptTemplate", "BaseStringPromptTemplate", "BasePromptTemplate", "Runnable"], "category": "Prompts", "description": "Schema to represent a basic prompt for an LLM", "inputParams": [{"label": "Template", "name": "template", "type": "string", "rows": 4, "placeholder": "What is a good name for a company that makes {product}?", "id": "promptTemplate_1-input-template-string"}, {"label": "Format Prompt Values", "name": "promptValues", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "promptTemplate_1-input-promptValues-json"}], "inputAnchors": [], "inputs": {"template": "Given the following user question, corresponding SQL query, and SQL result, answer the user question as details as possible.\n\nQuestion: {question}\n\nSQL Query: {sqlQuery}\n\nSQL Result: {sqlResponse}\n\nAnswer:\n", "promptValues": "{\"question\":\"{{question}}\",\"sqlResponse\":\"{{customFunction_1.data.instance}}\",\"sqlQuery\":\"{{getVariable_1.data.instance}}\"}"}, "outputAnchors": [{"id": "promptTemplate_1-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable", "name": "promptTemplate", "label": "PromptTemplate", "type": "PromptTemplate | BaseStringPromptTemplate | BasePromptTemplate | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "dragging": false, "positionAbsolute": {"x": 2655.2632506040304, "y": 218.145615216618}}, {"width": 300, "height": 304, "id": "getVariable_1", "position": {"x": 2272.8555266616872, "y": 24.11364076336241}, "type": "customNode", "data": {"id": "getVariable_1", "label": "Get Variable", "version": 2, "name": "getVariable", "type": "GetVariable", "baseClasses": ["GetVariable", "Utilities"], "tags": ["Utilities"], "category": "Utilities", "description": "Get variable that was saved using Set Variable node", "inputParams": [{"label": "Variable Name", "name": "variableName", "type": "string", "placeholder": "var1", "id": "getVariable_1-input-variableName-string"}], "inputAnchors": [], "inputs": {"variableName": "sqlQuery"}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "description": "", "options": [{"id": "getVariable_1-output-output-string|number|boolean|json|array", "name": "output", "label": "Output", "description": "", "type": "string | number | boolean | json | array"}], "default": "output"}], "outputs": {"output": "output"}, "selected": false}, "positionAbsolute": {"x": 2272.8555266616872, "y": 24.11364076336241}, "selected": false, "dragging": false}, {"width": 300, "height": 355, "id": "setVariable_1", "position": {"x": 1516.338224315744, "y": -133.6986023683283}, "type": "customNode", "data": {"id": "setVariable_1", "label": "Set Variable", "version": 2, "name": "setVariable", "type": "SetVariable", "baseClasses": ["SetVariable", "Utilities"], "tags": ["Utilities"], "category": "Utilities", "description": "Set variable which can be retrieved at a later stage. Variable is only available during runtime.", "inputParams": [{"label": "Variable Name", "name": "variableName", "type": "string", "placeholder": "var1", "id": "setVariable_1-input-variableName-string"}], "inputAnchors": [{"label": "Input", "name": "input", "type": "string | number | boolean | json | array", "optional": true, "list": true, "id": "setVariable_1-input-input-string | number | boolean | json | array"}], "inputs": {"input": ["{{ifElseFunction_0.data.instance}}"], "variableName": "sqlQuery"}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "description": "", "options": [{"id": "setVariable_1-output-output-string|number|boolean|json|array", "name": "output", "label": "Output", "description": "", "type": "string | number | boolean | json | array"}], "default": "output"}], "outputs": {"output": "output"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1516.338224315744, "y": -133.6986023683283}, "dragging": false}, {"width": 300, "height": 765, "id": "ifElseFunction_0", "position": {"x": 1147.8020838770517, "y": -237.39478763322148}, "type": "customNode", "data": {"id": "ifElseFunction_0", "label": "IfElse Function", "version": 2, "name": "ifElseFunction", "type": "IfElseFunction", "baseClasses": ["IfElseFunction", "Utilities"], "tags": ["Utilities"], "category": "Utilities", "description": "Split flows based on If Else javascript functions", "inputParams": [{"label": "Input Variables", "name": "functionInputVariables", "description": "Input variables can be used in the function with prefix $. For example: $var", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "ifElseFunction_0-input-functionInputVariables-json"}, {"label": "IfElse Name", "name": "functionName", "type": "string", "optional": true, "placeholder": "If Condition Match", "id": "ifElseFunction_0-input-functionName-string"}, {"label": "If Function", "name": "ifFunction", "description": "Function must return a value", "type": "code", "rows": 2, "default": "if (\"hello\" == \"hello\") {\n    return true;\n}", "id": "ifElseFunction_0-input-ifFunction-code"}, {"label": "Else Function", "name": "elseFunction", "description": "Function must return a value", "type": "code", "rows": 2, "default": "return false;", "id": "ifElseFunction_0-input-elseFunction-code"}], "inputAnchors": [], "inputs": {"functionInputVariables": "{\"sqlQuery\":\"{{llmChain_0.data.instance}}\"}", "functionName": "IF SQL Query contains SELECT and WHERE", "ifFunction": "const sqlQuery = $sqlQuery.trim();\n\nconst regex = /SELECT\\s.*?(?:\\n|$)/gi;\n\n// Extracting the SQL part\nconst matches = sqlQuery.match(regex);\nconst cleanSql = matches ? matches[0].trim() : \"\";\n\nif (cleanSql.includes(\"SELECT\") && cleanSql.includes(\"WHERE\")) {\n    return cleanSql;\n}", "elseFunction": "return $sqlQuery;"}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "description": "", "options": [{"id": "ifElseFunction_0-output-returnTrue-string|number|boolean|json|array", "name": "returnTrue", "label": "True", "description": "", "type": "string | number | boolean | json | array"}, {"id": "ifElseFunction_0-output-returnFalse-string|number|boolean|json|array", "name": "returnFalse", "label": "False", "description": "", "type": "string | number | boolean | json | array"}], "default": "returnTrue"}], "outputs": {"output": "returnTrue"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1147.8020838770517, "y": -237.39478763322148}, "dragging": false}, {"width": 300, "height": 511, "id": "promptTemplate_2", "position": {"x": 1193.7489579044463, "y": 615.4009446588724}, "type": "customNode", "data": {"id": "promptTemplate_2", "label": "Prompt Template", "version": 1, "name": "promptTemplate", "type": "PromptTemplate", "baseClasses": ["PromptTemplate", "BaseStringPromptTemplate", "BasePromptTemplate", "Runnable"], "category": "Prompts", "description": "Schema to represent a basic prompt for an LLM", "inputParams": [{"label": "Template", "name": "template", "type": "string", "rows": 4, "placeholder": "What is a good name for a company that makes {product}?", "id": "promptTemplate_2-input-template-string"}, {"label": "Format Prompt Values", "name": "promptValues", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "promptTemplate_2-input-promptValues-json"}], "inputAnchors": [], "inputs": {"template": "Politely say \"I'm not able to answer query\"", "promptValues": "{\"schema\":\"{{setVariable_0.data.instance}}\",\"question\":\"{{question}}\"}"}, "outputAnchors": [{"id": "promptTemplate_2-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable", "name": "promptTemplate", "label": "PromptTemplate", "type": "PromptTemplate | BaseStringPromptTemplate | BasePromptTemplate | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1193.7489579044463, "y": 615.4009446588724}, "dragging": false}, {"width": 300, "height": 669, "id": "chatOpenAI_2", "position": {"x": 1545.1023725538003, "y": 493.5495798408175}, "type": "customNode", "data": {"id": "chatOpenAI_2", "label": "ChatOpenAI", "version": 6, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_2-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_2-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_2-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_2-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_2-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_2-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-3.5-turbo-16k", "temperature": "0.7", "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": true, "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_2-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1545.1023725538003, "y": 493.5495798408175}, "dragging": false}, {"width": 300, "height": 507, "id": "llmChain_2", "position": {"x": 1914.509823868027, "y": 622.3435967391327}, "type": "customNode", "data": {"id": "llmChain_2", "label": "LLM Chain", "version": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON>", "BaseChain", "Runnable"], "category": "Chains", "description": "Chain to run queries against LLMs", "inputParams": [{"label": "Chain Name", "name": "chainName", "type": "string", "placeholder": "Name Your Chain", "optional": true, "id": "llmChain_2-input-chainName-string"}], "inputAnchors": [{"label": "Language Model", "name": "model", "type": "BaseLanguageModel", "id": "llmChain_2-input-model-BaseLanguageModel"}, {"label": "Prompt", "name": "prompt", "type": "BasePromptTemplate", "id": "llmChain_2-input-prompt-BasePromptTemplate"}, {"label": "Output Parser", "name": "outputParser", "type": "BaseLLMOutputParser", "optional": true, "id": "llmChain_2-input-outputParser-BaseLLMOutputParser"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "llmChain_2-input-inputModeration-Moderation"}], "inputs": {"model": "{{chatOpenAI_2.data.instance}}", "prompt": "{{promptTemplate_2.data.instance}}", "outputParser": "", "inputModeration": "", "chainName": "Fallback Chain"}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "llmChain_2-output-llmChain-LLMChain|BaseChain|Runnable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "LLM Chain", "type": "LLMChain | BaseChain | Runnable"}, {"id": "llmChain_2-output-outputPrediction-string|json", "name": "outputPrediction", "label": "Output Prediction", "type": "string | json"}], "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "outputs": {"output": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1914.509823868027, "y": 622.3435967391327}, "dragging": false}, {"id": "stickyNote_0", "position": {"x": -18.950231412347364, "y": -192.2980180516393}, "type": "stickyNote", "data": {"id": "stickyNote_0", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_0-input-note-string"}], "inputAnchors": [], "inputs": {"note": "First, get SQL database schema"}, "outputAnchors": [{"id": "stickyNote_0-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 42, "selected": false, "positionAbsolute": {"x": -18.950231412347364, "y": -192.2980180516393}, "dragging": false}, {"id": "stickyNote_1", "position": {"x": 1510.6324834799852, "y": -221.78240261184442}, "type": "stickyNote", "data": {"id": "stickyNote_1", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_1-input-note-string"}], "inputAnchors": [], "inputs": {"note": "Save as variable to be used at the last Prompt Template"}, "outputAnchors": [{"id": "stickyNote_1-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 62, "selected": false, "positionAbsolute": {"x": 1510.6324834799852, "y": -221.78240261184442}, "dragging": false}, {"id": "stickyNote_2", "position": {"x": 386.88037412001086, "y": 47.66735767574478}, "type": "stickyNote", "data": {"id": "stickyNote_2", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_2-input-note-string"}], "inputAnchors": [], "inputs": {"note": "Instruct LLM to return a SQL query using the schema.\n\nRecommend to give few examples for higher accuracy. \n\nChange the prompt accordingly to suit the type of database you are using"}, "outputAnchors": [{"id": "stickyNote_2-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 183, "selected": false, "positionAbsolute": {"x": 386.88037412001086, "y": 47.66735767574478}, "dragging": false}, {"id": "stickyNote_3", "position": {"x": 1148.366177280569, "y": -330.2148999791981}, "type": "stickyNote", "data": {"id": "stickyNote_3", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_3-input-note-string"}], "inputAnchors": [], "inputs": {"note": "Check if SQL Query is valid\n\nIf not, avoid executing it and return to user "}, "outputAnchors": [{"id": "stickyNote_3-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 82, "selected": false, "positionAbsolute": {"x": 1148.366177280569, "y": -330.2148999791981}, "dragging": false}, {"id": "stickyNote_4", "position": {"x": 1881.2554569013519, "y": -435.79147130381756}, "type": "stickyNote", "data": {"id": "stickyNote_4", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_4-input-note-string"}], "inputAnchors": [], "inputs": {"note": "Execute the SQL query after validated, and get the list of results back.\n\nTo avoid long list of results overflowing token limit, try capping the length of result here"}, "outputAnchors": [{"id": "stickyNote_4-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 143, "selected": false, "positionAbsolute": {"x": 1881.2554569013519, "y": -435.79147130381756}, "dragging": false}, {"id": "stickyNote_5", "position": {"x": 1545.0242031958799, "y": 428.37859733277077}, "type": "stickyNote", "data": {"id": "stickyNote_5", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_5-input-note-string"}], "inputAnchors": [], "inputs": {"note": "Fallback answer if SQL query is not valid"}, "outputAnchors": [{"id": "stickyNote_5-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 42, "selected": false, "positionAbsolute": {"x": 1545.0242031958799, "y": 428.37859733277077}, "dragging": false}, {"id": "stickyNote_6", "position": {"x": 2653.037036258241, "y": 53.55638699917168}, "type": "stickyNote", "data": {"id": "stickyNote_6", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_6-input-note-string"}], "inputAnchors": [], "inputs": {"note": "This is the final prompt.\n\nCombine the following:\nQuestion + SQL query + SQL result\n\nto generate a final answer"}, "outputAnchors": [{"id": "stickyNote_6-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 143, "selected": false, "positionAbsolute": {"x": 2653.037036258241, "y": 53.55638699917168}, "dragging": false}, {"id": "stickyNote_7", "position": {"x": 2267.355938520518, "y": -56.64296923028309}, "type": "stickyNote", "data": {"id": "stickyNote_7", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_7-input-note-string"}], "inputAnchors": [], "inputs": {"note": "Get the saved variable value to be used in prompt"}, "outputAnchors": [{"id": "stickyNote_7-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 62, "selected": false, "positionAbsolute": {"x": 2267.355938520518, "y": -56.64296923028309}, "dragging": false}], "edges": [{"source": "promptTemplate_0", "sourceHandle": "promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable", "target": "llmChain_0", "targetHandle": "llmChain_0-input-prompt-BasePromptTemplate", "type": "buttonedge", "id": "promptTemplate_0-promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable-llmChain_0-llmChain_0-input-prompt-BasePromptTemplate", "data": {"label": ""}}, {"source": "chatOpenAI_0", "sourceHandle": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "llmChain_0", "targetHandle": "llmChain_0-input-model-BaseLanguageModel", "type": "buttonedge", "id": "chatOpenAI_0-chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-llmChain_0-llmChain_0-input-model-BaseLanguageModel", "data": {"label": ""}}, {"source": "chatOpenAI_1", "sourceHandle": "chatOpenAI_1-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "llmChain_1", "targetHandle": "llmChain_1-input-model-BaseLanguageModel", "type": "buttonedge", "id": "chatOpenAI_1-chatOpenAI_1-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-llmChain_1-llmChain_1-input-model-BaseLanguageModel", "data": {"label": ""}}, {"source": "customFunction_1", "sourceHandle": "customFunction_1-output-output-string|number|boolean|json|array", "target": "promptTemplate_1", "targetHandle": "promptTemplate_1-input-promptValues-json", "type": "buttonedge", "id": "customFunction_1-customFunction_1-output-output-string|number|boolean|json|array-promptTemplate_1-promptTemplate_1-input-promptValues-json", "data": {"label": ""}}, {"source": "promptTemplate_1", "sourceHandle": "promptTemplate_1-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable", "target": "llmChain_1", "targetHandle": "llmChain_1-input-prompt-BasePromptTemplate", "type": "buttonedge", "id": "promptTemplate_1-promptTemplate_1-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable-llmChain_1-llmChain_1-input-prompt-BasePromptTemplate", "data": {"label": ""}}, {"source": "getVariable_1", "sourceHandle": "getVariable_1-output-output-string|number|boolean|json|array", "target": "promptTemplate_1", "targetHandle": "promptTemplate_1-input-promptValues-json", "type": "buttonedge", "id": "getVariable_1-getVariable_1-output-output-string|number|boolean|json|array-promptTemplate_1-promptTemplate_1-input-promptValues-json", "data": {"label": ""}}, {"source": "setVariable_1", "sourceHandle": "setVariable_1-output-output-string|number|boolean|json|array", "target": "customFunction_1", "targetHandle": "customFunction_1-input-functionInputVariables-json", "type": "buttonedge", "id": "setVariable_1-setVariable_1-output-output-string|number|boolean|json|array-customFunction_1-customFunction_1-input-functionInputVariables-json", "data": {"label": ""}}, {"source": "llmChain_0", "sourceHandle": "llmChain_0-output-outputPrediction-string|json", "target": "ifElseFunction_0", "targetHandle": "ifElseFunction_0-input-functionInputVariables-json", "type": "buttonedge", "id": "llmChain_0-llmChain_0-output-outputPrediction-string|json-ifElseFunction_0-ifElseFunction_0-input-functionInputVariables-json"}, {"source": "ifElseFunction_0", "sourceHandle": "ifElseFunction_0-output-returnTrue-string|number|boolean|json|array", "target": "setVariable_1", "targetHandle": "setVariable_1-input-input-string | number | boolean | json | array", "type": "buttonedge", "id": "ifElseFunction_0-ifElseFunction_0-output-returnTrue-string|number|boolean|json|array-setVariable_1-setVariable_1-input-input-string | number | boolean | json | array"}, {"source": "ifElseFunction_0", "sourceHandle": "ifElseFunction_0-output-returnFalse-string|number|boolean|json|array", "target": "promptTemplate_2", "targetHandle": "promptTemplate_2-input-promptValues-json", "type": "buttonedge", "id": "ifElseFunction_0-ifElseFunction_0-output-returnFalse-string|number|boolean|json|array-promptTemplate_2-promptTemplate_2-input-promptValues-json"}, {"source": "chatOpenAI_2", "sourceHandle": "chatOpenAI_2-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "llmChain_2", "targetHandle": "llmChain_2-input-model-BaseLanguageModel", "type": "buttonedge", "id": "chatOpenAI_2-chatOpenAI_2-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-llmChain_2-llmChain_2-input-model-BaseLanguageModel"}, {"source": "promptTemplate_2", "sourceHandle": "promptTemplate_2-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable", "target": "llmChain_2", "targetHandle": "llmChain_2-input-prompt-BasePromptTemplate", "type": "buttonedge", "id": "promptTemplate_2-promptTemplate_2-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable-llmChain_2-llmChain_2-input-prompt-BasePromptTemplate"}, {"source": "customFunction_2", "sourceHandle": "customFunction_2-output-output-string|number|boolean|json|array", "target": "promptTemplate_0", "targetHandle": "promptTemplate_0-input-promptValues-json", "type": "buttonedge", "id": "customFunction_2-customFunction_2-output-output-string|number|boolean|json|array-promptTemplate_0-promptTemplate_0-input-promptValues-json"}]}