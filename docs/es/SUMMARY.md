# Tabla de Contenidos

-   [Introducción](README.md)
-   [Características de Universo Platformo](universo-platformo/README.md)
    -   [Sistema de Nodos UPDL](universo-platformo/updl-nodes/README.md)
    -   [Plantillas MMOOMM](universo-platformo/mmoomm-templates/README.md)
    -   [Exportación Multiplataforma](universo-platformo/export/README.md)
    -   [Sistemas de Recursos Mejorados](universo-platformo/resources/README.md)
-   [Hoja de Ruta](roadmap/README.md)
    -   [Arquitectura Actual](roadmap/current-architecture/README.md)
        -   [Aplicaciones Existentes](roadmap/current-architecture/existing-apps.md)
        -   [Análisis de Packages](roadmap/current-architecture/packages-analysis.md)
        -   [Patrones de Integración](roadmap/current-architecture/integration-patterns.md)
    -   [Arquitectura Objetivo](roadmap/target-architecture/README.md)
        -   [Aplicaciones MMOOMM](roadmap/target-architecture/mmoomm-apps.md)
        -   [Aplicaciones Base de la Plataforma](roadmap/target-architecture/core-platform-apps.md)
        -   [Diseño de Microservicios](roadmap/target-architecture/microservices-design.md)
        -   [Estrategia de Orquestación](roadmap/target-architecture/orchestration-strategy.md)
    -   [Plan de Implementación](roadmap/implementation-plan/README.md)
        -   [Fase 1: MVP](roadmap/implementation-plan/phase-1-mvp.md)
        -   [Fase 2: Sistemas Base](roadmap/implementation-plan/phase-2-core.md)
        -   [Fase 3: Funciones Avanzadas](roadmap/implementation-plan/phase-3-advanced.md)
        -   [Fase 4: Ecosistema](roadmap/implementation-plan/phase-4-ecosystem.md)
        -   [Estrategia de Migración](roadmap/implementation-plan/migration-strategy.md)
    -   [Aplicaciones](roadmap/applications/README.md)
        -   [Mecánicas de Juego](roadmap/applications/game-mechanics/README.md)
        -   [Sistemas Sociales](roadmap/applications/social-systems/README.md)
        -   [Sistemas Técnicos](roadmap/applications/technical-systems/README.md)
        -   [Núcleo de la Plataforma](roadmap/applications/platform-core/README.md)
    -   [Especificaciones Técnicas](roadmap/technical-specifications/README.md)
        -   [Diseño de Base de Datos](roadmap/technical-specifications/database-design.md)
        -   [Especificaciones de API](roadmap/technical-specifications/api-specifications.md)
        -   [Requisitos de Seguridad](roadmap/technical-specifications/security-requirements.md)
        -   [Objetivos de Rendimiento](roadmap/technical-specifications/performance-targets.md)
    -   [Hitos](roadmap/milestones/README.md)
        -   [v0.22.0-alpha](roadmap/milestones/v0.22.0-alpha.md)
        -   [v0.25.0-beta](roadmap/milestones/v0.25.0-beta.md)
        -   [v1.0.0-release](roadmap/milestones/v1.0.0-release.md)
        -   [Versiones Futuras](roadmap/milestones/future-versions.md)
-   [Aplicaciones](applications/README.md)
    -   [Sistema UPDL](applications/updl/README.md)
    -   [Sistema de Publicación](applications/publish/README.md)
    -   [Gestión de Perfiles](applications/profile/README.md)
    -   [Sistema de Análisis](applications/analytics/README.md)
    -   [Sistema de Autenticación](applications/auth/README.md)

## Partes

-   [Parte 1: Introducción](partes/parte-1-introduccion/README.md)
    -   [Recursos](partes/parte-1-introduccion/recursos.md)
-   [Parte 2: Chains Avanzadas](partes/parte-2-chains-avanzadas/README.md)
    -   [Desafío 1: Traductor de Lenguas Antiguas](partes/parte-2-chains-avanzadas/desafio-1-traductor-de-lenguas-antiguas.md)
-   [Parte 3: Gestión de Documentos y Memoria](partes/parte-3-gestion-de-documentos-y-memoria/README.md)
    -   [Desafío 2: Chatbot Nikola Tesla](partes/parte-3-gestion-de-documentos-y-memoria/desafio-2-chatbot-nikola-tesla.md)
-   [Parte 4: Despliegue y API](partes/parte-4-despliegue-y-api.md)
-   [Parte 5: Introducción a Agentes](partes/parte-5-introduccion-a-agentes/README.md)
    -   [Desafíos](partes/parte-5-introduccion-a-agentes/desafios.md)
-   [Parte 6: Agentes Avanzados](partes/parte-6-agentes-avanzados/README.md)
    -   [Desafíos](partes/parte-6-agentes-avanzados/desafios.md)
-   [Parte 7: Multi-Agentes](partes/parte-7-multi-agentes/README.md)
    -   [Desafíos](partes/parte-7-multi-agentes/desafios.md)
-   [Parte 8: AgentFlows](partes/parte-8-agentflows/README.md)
    -   [Desafíos](partes/parte-8-agentflows/desafios.md)
-   [Parte 9: Agentes Secuenciales](partes/parte-9-agentes-secuenciales/README.md)
    -   [Desafíos](partes/parte-9-agentes-secuenciales/desafios.md)
-   [Parte 10: Sequential Agents Avanzados](partes/parte-10-sequential-agents-avanzados.md)

## Recursos

-   [Enlaces Útiles](recursos/enlaces-utiles.md)
-   [Documentación Oficial](recursos/documentacion-oficial.md)
-   [Ejemplos de Código](recursos/ejemplos-de-codigo.md)
-   [Mejores Prácticas](recursos/mejores-practicas.md)

## Documentación Oficial

-   [Introduction](README.md)
-   [Primeros Pasos](primeros-pasos/README.md)
-   [Guía de Contribución](contribucion/README.md)
    -   [Building Node](contribucion/building-node.md)
-   [Referencia de API](referencia-api/README.md)
    -   [Assistants](referencia-api/assistants.md)
    -   [Attachments](referencia-api/attachments.md)
    -   [Chat Message](referencia-api/chat-message.md)
    -   [Chatflows](referencia-api/chatflows.md)
    -   [Document Store](referencia-api/document-store.md)
    -   [Feedback](referencia-api/feedback.md)
    -   [Leads](referencia-api/leads.md)
    -   [Ping](referencia-api/ping.md)
    -   [Prediction](referencia-api/prediction.md)
    -   [Tools](referencia-api/tools.md)
    -   [Upsert History](referencia-api/upsert-history.md)
    -   [Variables](referencia-api/variables.md)
    -   [Vector Upsert](referencia-api/vector-upsert.md)
-   [Usar Flowise](usar-flowise/README.md)
    -   [Agentflows](usar-flowise/agentflows/README.md)
        -   [Multi-Agents](usar-flowise/agentflows/multi-agents.md)
        -   [Sequential Agents](usar-flowise/agentflows/sequential-agents.md)
            -   [Tutoriales en Video](usar-flowise/agentflows/sequential-agents/tutoriales-video.md)
    -   [API](usar-flowise/api.md)
    -   [Analytic](usar-flowise/analytic.md)
    -   [Almacenes de Documentos](usar-flowise/almacenes-documentos.md)
    -   [Embed](usar-flowise/embed.md)
    -   [Monitoring](usar-flowise/monitoring.md)
    -   [Streaming](usar-flowise/streaming.md)
    -   [Telemetry](usar-flowise/telemetry.md)
    -   [Subidas](usar-flowise/subidas.md)
    -   [Variables](usar-flowise/variables.md)
    -   [Workspaces](usar-flowise/workspaces.md)
    -   [Evaluaciones](usar-flowise/evaluaciones.md)
-   [Configuración](configuracion/README.md)
    -   [Auth](configuracion/autorizacion/README.md)
        -   [Nivel de App](configuracion/autorizacion/nivel-app.md)
        -   [Nivel de Chatflow](configuracion/autorizacion/nivel-chatflow.md)
    -   [Databases](configuracion/databases.md)
    -   [Deployment](configuracion/deployment/README.md)
        -   [AWS](configuracion/deployment/aws.md)
        -   [Azure](configuracion/deployment/azure.md)
        -   [Alibaba Cloud](https://aliyun-computenest.github.io/quickstart-flowise/)
        -   [Digital Ocean](configuracion/deployment/digital-ocean.md)
        -   [Elestio](https://elest.io/open-source/flowiseai)
        -   [GCP](configuracion/deployment/gcp.md)
        -   [Hugging Face](configuracion/deployment/hugging-face.md)
        -   [Kubernetes using Helm](https://artifacthub.io/packages/helm/cowboysysop/flowise)
        -   [Railway](configuracion/deployment/railway.md)
        -   [Render](configuracion/deployment/render.md)
        -   [Replit](configuracion/deployment/replit.md)
        -   [RepoCloud](https://repocloud.io/details/?app_id=29)
        -   [Sealos](configuracion/deployment/sealos.md)
        -   [Zeabur](configuracion/deployment/zeabur.md)
    -   [Variables de Entorno](configuracion/variables-entorno.md)
    -   [Rate Limit](configuracion/rate-limit.md)
    -   [Ejecutar Flowise detrás de proxy corporativo](configuracion/ejecutar-detras-proxy.md)
    -   [SSO](configuracion/sso.md)
-   [Integraciones](integraciones/README.md)
    -   [LangChain](integraciones/langchain/README.md)
        -   [Agents](integraciones/langchain/agents/README.md)
            -   [Airtable Agent](integraciones/langchain/agents/airtable-agent.md)
            -   [AutoGPT](integraciones/langchain/agents/autogpt.md)
            -   [BabyAGI](integraciones/langchain/agents/babyagi.md)
            -   [CSV Agent](integraciones/langchain/agents/csv-agent.md)
            -   [Conversational Agent](integraciones/langchain/agents/conversational-agent.md)
            -   [Conversational Retrieval Agent](integraciones/langchain/agents/conversational-retrieval-agent.md)
            -   [MistralAI Tool Agent](integraciones/langchain/agents/mistralai-tool-agent.md)
            -   [OpenAI Assistant](integraciones/langchain/agents/openai-assistant/README.md)
                -   [Threads](integraciones/langchain/agents/openai-assistant/threads.md)
            -   [OpenAI Function Agent](integraciones/langchain/agents/openai-function-agent.md)
            -   [OpenAI Tool Agent](integraciones/langchain/agents/openai-tool-agent.md)
            -   [ReAct Agent Chat](integraciones/langchain/agents/react-agent-chat.md)
            -   [ReAct Agent LLM](integraciones/langchain/agents/react-agent-llm.md)
            -   [Tool Agent](integraciones/langchain/agents/tool-agent.md)
            -   [XML Agent](integraciones/langchain/agents/xml-agent.md)
        -   [Cache](integraciones/langchain/cache/README.md)
            -   [InMemory Cache](integraciones/langchain/cache/in-memory-cache.md)
            -   [InMemory Embedding Cache](integraciones/langchain/cache/inmemory-embedding-cache.md)
            -   [Momento Cache](integraciones/langchain/cache/momento-cache.md)
            -   [Redis Cache](integraciones/langchain/cache/redis-cache.md)
            -   [Redis Embeddings Cache](integraciones/langchain/cache/redis-embeddings-cache.md)
            -   [Upstash Redis Cache](integraciones/langchain/cache/upstash-redis-cache.md)
        -   [Chains](integraciones/langchain/chains/README.md)
            -   [GET API Chain](integraciones/langchain/chains/get-api-chain.md)
            -   [OpenAPI Chain](integraciones/langchain/chains/openapi-chain.md)
            -   [POST API Chain](integraciones/langchain/chains/post-api-chain.md)
            -   [Conversation Chain](integraciones/langchain/chains/conversation-chain.md)
            -   [Conversational Retrieval QA Chain](integraciones/langchain/chains/conversational-retrieval-qa-chain.md)
            -   [LLM Chain](integraciones/langchain/chains/llm-chain.md)
            -   [Multi Prompt Chain](integraciones/langchain/chains/multi-prompt-chain.md)
            -   [Multi Retrieval QA Chain](integraciones/langchain/chains/multi-retrieval-qa-chain.md)
            -   [Retrieval QA Chain](integraciones/langchain/chains/retrieval-qa-chain.md)
            -   [Sql Database Chain](integraciones/langchain/chains/sql-database-chain.md)
            -   [Vectara QA Chain](integraciones/langchain/chains/vectara-chain.md)
            -   [VectorDB QA Chain](integraciones/langchain/chains/vectordb-qa-chain.md)
        -   [Chat Models](integraciones/langchain/chat-models/README.md)
            -   [AWS ChatBedrock](integraciones/langchain/chat-models/aws-chatbedrock.md)
            -   [Azure ChatOpenAI](integraciones/langchain/chat-models/azure-chatopenai-1.md)
            -   [NVIDIA NIM](integraciones/langchain/chat-models/nvidia-nim.md)
            -   [ChatAnthropic](integraciones/langchain/chat-models/chatanthropic.md)
            -   [ChatCohere](integraciones/langchain/chat-models/chatcohere.md)
            -   [Chat Fireworks](integraciones/langchain/chat-models/chat-fireworks.md)
            -   [ChatGoogleGenerativeAI](integraciones/langchain/chat-models/google-ai.md)
            -   [Google VertexAI](integraciones/langchain/chat-models/google-vertexai.md)
            -   [ChatHuggingFace](integraciones/langchain/chat-models/chathuggingface.md)
            -   [ChatLocalAI](integraciones/langchain/chat-models/chatlocalai.md)
            -   [ChatMistralAI](integraciones/langchain/chat-models/mistral-ai.md)
            -   [IBM Watsonx](integraciones/langchain/chat-models/ibm-watsonx.md)
            -   [ChatOllama](integraciones/langchain/chat-models/chatollama.md)
            -   [ChatOpenAI](integraciones/langchain/chat-models/azure-chatopenai.md)
            -   [ChatTogetherAI](integraciones/langchain/chat-models/chattogetherai.md)
            -   [GroqChat](integraciones/langchain/chat-models/groqchat.md)
        -   [Document Loaders](integraciones/langchain/document-loaders/README.md)
            -   [API Loader](integraciones/langchain/document-loaders/api-loader.md)
            -   [Airtable](integraciones/langchain/document-loaders/airtable.md)
            -   [Apify Website Content Crawler](integraciones/langchain/document-loaders/apify-website-content-crawler.md)
            -   [Cheerio Web Scraper](integraciones/langchain/document-loaders/cheerio-web-scraper.md)
            -   [Confluence](integraciones/langchain/document-loaders/confluence.md)
            -   [Csv File](integraciones/langchain/document-loaders/csv-file.md)
            -   [Custom Document Loader](integraciones/langchain/document-loaders/custom-document-loader.md)
            -   [Document Store](integraciones/langchain/document-loaders/document-store.md)
            -   [Docx File](integraciones/langchain/document-loaders/docx-file.md)
            -   [File Loader](integraciones/langchain/document-loaders/file-loader.md)
            -   [Figma](integraciones/langchain/document-loaders/figma.md)
            -   [FireCrawl](integraciones/langchain/document-loaders/firecrawl.md)
            -   [Folder with Files](integraciones/langchain/document-loaders/folder-with-files.md)
            -   [GitBook](integraciones/langchain/document-loaders/gitbook.md)
            -   [Github](integraciones/langchain/document-loaders/github.md)
            -   [Json File](integraciones/langchain/document-loaders/json-file.md)
            -   [Json Lines File](integraciones/langchain/document-loaders/json-lines-file.md)
            -   [Notion Database](integraciones/langchain/document-loaders/notion-database.md)
            -   [Notion Folder](integraciones/langchain/document-loaders/notion-folder.md)
            -   [Notion Page](integraciones/langchain/document-loaders/notion-page.md)
            -   [PDF Files](integraciones/langchain/document-loaders/pdf-file.md)
            -   [Plain Text](integraciones/langchain/document-loaders/plain-text.md)
            -   [Playwright Web Scraper](integraciones/langchain/document-loaders/playwright-web-scraper.md)
            -   [Puppeteer Web Scraper](integraciones/langchain/document-loaders/puppeteer-web-scraper.md)
            -   [S3 File Loader](integraciones/langchain/document-loaders/s3-file-loader.md)
            -   [SearchApi For Web Search](integraciones/langchain/document-loaders/searchapi-for-web-search.md)
            -   [SerpApi For Web Search](integraciones/langchain/document-loaders/serpapi-for-web-search.md)
            -   [Spider Web Scraper/Crawler](integraciones/langchain/document-loaders/spider-web-scraper-crawler.md)
            -   [Text File](integraciones/langchain/document-loaders/text-file.md)
            -   [Unstructured File Loader](integraciones/langchain/document-loaders/unstructured-file-loader.md)
            -   [Unstructured Folder Loader](integraciones/langchain/document-loaders/unstructured-folder-loader.md)
            -   [VectorStore To Document](integraciones/langchain/document-loaders/vectorstore-to-document.md)
        -   [Embeddings](integraciones/langchain/embeddings/README.md)
            -   [AWS Bedrock Embeddings](integraciones/langchain/embeddings/aws-bedrock-embeddings.md)
            -   [Azure OpenAI Embeddings](integraciones/langchain/embeddings/azure-openai-embeddings.md)
            -   [Cohere Embeddings](integraciones/langchain/embeddings/cohere-embeddings.md)
            -   [Google GenerativeAI Embeddings](integraciones/langchain/embeddings/googlegenerativeai-embeddings.md)
            -   [Google VertexAI Embeddings](integraciones/langchain/embeddings/googlevertexai-embeddings.md)
            -   [HuggingFace Inference Embeddings](integraciones/langchain/embeddings/huggingface-inference-embeddings.md)
            -   [LocalAI Embeddings](integraciones/langchain/embeddings/localai-embeddings.md)
            -   [MistralAI Embeddings](integraciones/langchain/embeddings/mistralai-embeddings.md)
            -   [Ollama Embeddings](integraciones/langchain/embeddings/ollama-embeddings.md)
            -   [OpenAI Embeddings](integraciones/langchain/embeddings/openai-embeddings.md)
            -   [OpenAI Embeddings Custom](integraciones/langchain/embeddings/openai-embeddings-custom.md)
            -   [TogetherAI Embedding](integraciones/langchain/embeddings/togetherai-embedding.md)
            -   [VoyageAI Embeddings](integraciones/langchain/embeddings/voyageai-embeddings.md)
        -   [LLMs](integraciones/langchain/llms/README.md)
            -   [AWS Bedrock](integraciones/langchain/llms/aws-bedrock.md)
            -   [Azure OpenAI](integraciones/langchain/llms/azure-openai.md)
            -   [Cohere](integraciones/langchain/llms/cohere.md)
            -   [GoogleVertex AI](integraciones/langchain/llms/googlevertex-ai.md)
            -   [HuggingFace Inference](integraciones/langchain/llms/huggingface-inference.md)
            -   [Ollama](integraciones/langchain/llms/ollama.md)
            -   [OpenAI](integraciones/langchain/llms/openai.md)
            -   [Replicate](integraciones/langchain/llms/replicate.md)
        -   [Memory](integraciones/langchain/memory/README.md)
            -   [Buffer Memory](integraciones/langchain/memory/buffer-memory.md)
            -   [Buffer Window Memory](integraciones/langchain/memory/buffer-window-memory.md)
            -   [Conversation Summary Memory](integraciones/langchain/memory/conversation-summary-memory.md)
            -   [Conversation Summary Buffer Memory](integraciones/langchain/memory/conversation-summary-buffer-memory.md)
            -   [DynamoDB Chat Memory](integraciones/langchain/memory/dynamodb-chat-memory.md)
            -   [MongoDB Atlas Chat Memory](integraciones/langchain/memory/mongodb-atlas-chat-memory.md)
            -   [Redis-Backed Chat Memory](integraciones/langchain/memory/redis-backed-chat-memory.md)
            -   [Upstash Redis-Backed Chat Memory](integraciones/langchain/memory/upstash-redis-backed-chat-memory.md)
            -   [Zep Memory](integraciones/langchain/memory/zep-memory.md)
        -   [Moderation](integraciones/langchain/moderation/README.md)
            -   [OpenAI Moderation](integraciones/langchain/moderation/openai-moderation.md)
            -   [Simple Prompt Moderation](integraciones/langchain/moderation/simple-prompt-moderation.md)
        -   [Output Parsers](integraciones/langchain/output-parsers/README.md)
            -   [CSV Output Parser](integraciones/langchain/output-parsers/csv-output-parser.md)
            -   [Custom List Output Parser](integraciones/langchain/output-parsers/custom-list-output-parser.md)
            -   [Structured Output Parser](integraciones/langchain/output-parsers/structured-output-parser.md)
            -   [Advanced Structured Output Parser](integraciones/langchain/output-parsers/advanced-structured-output-parser.md)
        -   [Prompts](integraciones/langchain/prompts/README.md)
            -   [Chat Prompt Template](integraciones/langchain/prompts/chat-prompt-template.md)
            -   [Few Shot Prompt Template](integraciones/langchain/prompts/few-shot-prompt-template.md)
            -   [Prompt Template](integraciones/langchain/prompts/prompt-template.md)
        -   [Record Managers](integraciones/langchain/record-managers.md)
        -   [Retrievers](integraciones/langchain/retrievers/README.md)
            -   [Custom Retriever](integraciones/langchain/retrievers/custom-retriever.md)
            -   [Cohere Rerank Retriever](integraciones/langchain/retrievers/cohere-rerank-retriever.md)
            -   [Embeddings Filter Retriever](integraciones/langchain/retrievers/embeddings-filter-retriever.md)
            -   [HyDE Retriever](integraciones/langchain/retrievers/hyde-retriever.md)
            -   [LLM Filter Retriever](integraciones/langchain/retrievers/llm-filter-retriever.md)
            -   [Multi Query Retriever](integraciones/langchain/retrievers/multi-query-retriever.md)
            -   [Prompt Retriever](integraciones/langchain/retrievers/prompt-retriever.md)
            -   [Reciprocal Rank Fusion Retriever](integraciones/langchain/retrievers/reciprocal-rank-fusion-retriever.md)
            -   [Similarity Score Threshold Retriever](integraciones/langchain/retrievers/similarity-score-threshold-retriever.md)
            -   [Vector Store Retriever](integraciones/langchain/retrievers/vector-store-retriever.md)
            -   [Voyage AI Rerank Retriever](integraciones/langchain/retrievers/page.md)
        -   [Text Splitters](integraciones/langchain/text-splitters/README.md)
            -   [Character Text Splitter](integraciones/langchain/text-splitters/character-text-splitter.md)
            -   [Code Text Splitter](integraciones/langchain/text-splitters/code-text-splitter.md)
            -   [Html-To-Markdown Text Splitter](integraciones/langchain/text-splitters/html-to-markdown-text-splitter.md)
            -   [Markdown Text Splitter](integraciones/langchain/text-splitters/markdown-text-splitter.md)
            -   [Recursive Character Text Splitter](integraciones/langchain/text-splitters/recursive-character-text-splitter.md)
            -   [Token Text Splitter](integraciones/langchain/text-splitters/token-text-splitter.md)
        -   [Tools](integraciones/langchain/tools/README.md)
            -   [BraveSearch API](integraciones/langchain/tools/bravesearch-api.md)
            -   [Calculator](integraciones/langchain/tools/calculator.md)
            -   [Chain Tool](integraciones/langchain/tools/chain-tool.md)
            -   [Chatflow Tool](integraciones/langchain/tools/chatflow-tool.md)
            -   [Custom Tool](integraciones/langchain/tools/custom-tool.md)
            -   [Exa Search](integraciones/langchain/tools/exa-search.md)
            -   [Google Custom Search](integraciones/langchain/tools/google-custom-search.md)
            -   [OpenAPI Toolkit](integraciones/langchain/tools/openapi-toolkit.md)
            -   [Code Interpreter by E2B](integraciones/langchain/tools/python-interpreter.md)
            -   [Read File](integraciones/langchain/tools/read-file.md)
            -   [Request Get](integraciones/langchain/tools/request-get.md)
            -   [Request Post](integraciones/langchain/tools/request-post.md)
            -   [Retriever Tool](integraciones/langchain/tools/retriever-tool.md)
            -   [SearchApi](integraciones/langchain/tools/searchapi.md)
            -   [SearXNG](integraciones/langchain/tools/searxng.md)
            -   [Serp API](integraciones/langchain/tools/serp-api.md)
            -   [Serper](integraciones/langchain/tools/serper.md)
            -   [Web Browser](integraciones/langchain/tools/web-browser.md)
            -   [Write File](integraciones/langchain/tools/write-file.md)
        -   [Vector Stores](integraciones/langchain/vector-stores/README.md)
            -   [AstraDB](integraciones/langchain/vector-stores/astradb.md)
            -   [Chroma](integraciones/langchain/vector-stores/chroma.md)
            -   [Elastic](integraciones/langchain/vector-stores/elastic.md)
            -   [Faiss](integraciones/langchain/vector-stores/faiss.md)
            -   [In-Memory Vector Store](integraciones/langchain/vector-stores/in-memory-vector-store.md)
            -   [Milvus](integraciones/langchain/vector-stores/milvus.md)
            -   [MongoDB Atlas](integraciones/langchain/vector-stores/mongodb-atlas.md)
            -   [OpenSearch](integraciones/langchain/vector-stores/opensearch.md)
            -   [Pinecone](integraciones/langchain/vector-stores/pinecone.md)
            -   [Postgres](integraciones/langchain/vector-stores/postgres.md)
            -   [Qdrant](integraciones/langchain/vector-stores/qdrant.md)
            -   [Redis](integraciones/langchain/vector-stores/redis.md)
            -   [SingleStore](integraciones/langchain/vector-stores/singlestore.md)
            -   [Supabase](integraciones/langchain/vector-stores/supabase.md)
            -   [Upstash Vector](integraciones/langchain/vector-stores/upstash-vector.md)
            -   [Vectara](integraciones/langchain/vector-stores/vectara.md)
            -   [Weaviate](integraciones/langchain/vector-stores/weaviate.md)
            -   [Zep Collection - Open Source](integraciones/langchain/vector-stores/zep-collection-open-source.md)
            -   [Zep Collection - Cloud](integraciones/langchain/vector-stores/zep-collection-cloud.md)
    -   [LiteLLM Proxy](integraciones/litellm/README.md)
    -   [LlamaIndex](integraciones/llamaindex/README.md)
        -   [Agents](integraciones/llamaindex/agents/README.md)
            -   [OpenAI Tool Agent](integraciones/llamaindex/agents/openai-tool-agent.md)
            -   [Anthropic Tool Agent](integraciones/llamaindex/agents/openai-tool-agent-1.md)
        -   [Chat Models](integraciones/llamaindex/chat-models/README.md)
            -   [AzureChatOpenAI](integraciones/llamaindex/chat-models/azurechatopenai.md)
            -   [ChatAnthropic](integraciones/llamaindex/chat-models/chatanthropic.md)
            -   [ChatMistral](integraciones/llamaindex/chat-models/chatmistral.md)
            -   [ChatOllama](integraciones/llamaindex/chat-models/chatollama.md)
            -   [ChatOpenAI](integraciones/llamaindex/chat-models/chatopenai.md)
            -   [ChatTogetherAI](integraciones/llamaindex/chat-models/chattogetherai.md)
            -   [ChatGroq](integraciones/llamaindex/chat-models/chatgroq.md)
        -   [Embeddings](integraciones/llamaindex/embeddings/README.md)
            -   [Azure OpenAI Embeddings](integraciones/llamaindex/embeddings/azure-openai-embeddings.md)
            -   [OpenAI Embedding](integraciones/llamaindex/embeddings/openai-embedding.md)
        -   [Engine](integraciones/llamaindex/engine/README.md)
            -   [Query Engine](integraciones/llamaindex/engine/query-engine.md)
            -   [Simple Chat Engine](integraciones/llamaindex/engine/simple-chat-engine.md)
            -   [Context Chat Engine](integraciones/llamaindex/engine/context-chat-engine.md)
            -   [Sub-Question Query Engine](integraciones/llamaindex/engine/sub-question-query-engine.md)
        -   [Response Synthesizer](integraciones/llamaindex/response-synthesizer/README.md)
            -   [Refine](integraciones/llamaindex/response-synthesizer/refine.md)
            -   [Compact And Refine](integraciones/llamaindex/response-synthesizer/compact-and-refine.md)
            -   [Simple Response Builder](integraciones/llamaindex/response-synthesizer/simple-response-builder.md)
            -   [Tree Summarize](integraciones/llamaindex/response-synthesizer/tree-summarize.md)
        -   [Tools](integraciones/llamaindex/tools/README.md)
            -   [Query Engine Tool](integraciones/llamaindex/tools/query-engine-tool.md)
        -   [Vector Stores](integraciones/llamaindex/vector-stores/README.md)
            -   [Pinecone](integraciones/llamaindex/vector-stores/pinecone.md)
            -   [SimpleStore](integraciones/llamaindex/vector-stores/queryengine-tool.md)
    -   [Utilities](integraciones/utilities/README.md)
        -   [Custom JS Function](integraciones/utilities/custom-js-function.md)
        -   [Set/Get Variable](integraciones/utilities/set-get-variable.md)
        -   [If Else](integraciones/utilities/if-else.md)
        -   [Sticky Note](integraciones/utilities/sticky-note.md)
    -   [External Integrations](integraciones/3rd-party-platform-integration/README.md)
        -   [Zapier Zaps](integraciones/3rd-party-platform-integration/zapier-zaps.md)
-   [Migration Guide](migration-guide/README.md)
    -   [v1.3.0 Migration Guide](migration-guide/v1.3.0-migration-guide.md)
    -   [v1.4.3 Migration Guide](migration-guide/v1.4.3-migration-guide.md)
    -   [v2.1.4 Migration Guide](migration-guide/v2.1.4-migration-guide.md)
-   [Use Cases](use-cases/README.md)
    -   [Calling Children Flows](use-cases/calling-children-flows.md)
    -   [Calling Webhook](use-cases/webhook-tool.md)
    -   [Interacting with API](use-cases/interacting-with-api.md)
    -   [Multiple Documents QnA](use-cases/multiple-documents-qna.md)
    -   [SQL QnA](use-cases/sql-qna.md)
    -   [Upserting Data](use-cases/upserting-data.md)
    -   [Web Scrape QnA](use-cases/web-scrape-qna.md)

## Flowise

-   [Flowise GitHub](https://github.com/FlowiseAI)
-   [Flowise Cloud](https://flowiseai.com/join)
