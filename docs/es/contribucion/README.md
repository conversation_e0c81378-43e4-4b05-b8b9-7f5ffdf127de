---
description: Aprende cómo contribuir a este proyecto
---

# Guía de Contribución

***

¡Apreciamos todas las contribuciones! Sin importar tu nivel de habilidad o experiencia técnica, puedes ayudar a que este proyecto crezca. Aquí hay varias formas de contribuir:

## ⭐ Estrella

Dale una estrella y comparte el [Repositorio de Github](https://github.com/FlowiseAI/Flowise).

## 🙌 Comparte tu Chatflow

¡Sí! Compartir cómo usas Flowise es una forma de contribución. Exporta tu chatflow como JSON, adjunta una captura de pantalla y compártelo en la [sección Mostrar y Contar](https://github.com/FlowiseAI/Flowise/discussions/categories/show-and-tell).

## 💡 Ideas

Damos la bienvenida a ideas para nuevas funcionalidades e integraciones de aplicaciones. Envía tus sugerencias a la [sección de Ideas](https://github.com/FlowiseAI/Flowise/discussions/categories/ideas).

## 🙋 Preguntas y Respuestas

¿Quieres aprender más? Busca respuestas a cualquier pregunta en la [sección de Preguntas y Respuestas](https://github.com/FlowiseAI/Flowise/discussions/categories/q-a). Si no encuentras lo que buscas, no dudes en crear una nueva pregunta. Podría ayudar a otros que tengan preguntas similares.

## 🐞 Reportar Errores

¿Encontraste un problema? [Repórtalo](https://github.com/FlowiseAI/Flowise/issues/new/choose).

## 📖 Contribuir a la Documentación

1. Haz un fork del [Repositorio Oficial de Documentación de Flowise](https://github.com/FlowiseAI/FlowiseDocs)
2. Clona tu repositorio forkeado
3. Crea una nueva rama
4. Cambia a la rama que acabas de crear
5. Ve a la carpeta del repositorio

    ```bash
    cd FlowiseDocs
    ```
6. Realiza los cambios
7. Haz commit de los cambios y envía un Pull Request desde la rama forkeada apuntando a [FlowiseDocs main](https://github.com/FlowiseAI/FlowiseDocs)

## 👨‍💻 Contribuir al Código

Para aprender cómo contribuir código, ve a la sección [Para Desarrolladores](../getting-started/#setup-2) y sigue las instrucciones.

Si estás contribuyendo con una nueva integración de nodo, lee la guía de [Construcción de Nodos](building-node.md).

## 🏷️ Proceso de Pull Request

Un miembro del equipo de FlowiseAI será notificado/asignado automáticamente cuando abras un pull request. También puedes contactarnos en [Discord](https://discord.gg/jbaHfsRVBW).

## 📜 Código de Conducta

Este proyecto y todos sus participantes se rigen por el Código de Conducta que se puede encontrar en el [archivo](https://github.com/FlowiseAI/Flowise/blob/main/CODE\_OF\_CONDUCT.md). Al participar, se espera que cumplas con este código.

Por favor, reporta cualquier comportamiento <NAME_EMAIL>.
