---
description: Aprende Sequential Agents de la Comunidad
---

# Tutoriales en Video

### Construye un Agente RAG Multi-Etapa

En este video, [<PERSON>](https://youtube.com/@leonvanzyl) proporciona un tutorial paso a paso sobre cómo crear un agente RAG avanzado que incorpora técnicas de routing, fallback y auto-corrección.

{% embed url="https://youtu.be/OejuvdyN_U8" %}

### Domina Sequential Agents: Construye Apps AI Complejas con Flowise

En este video, [<PERSON>](https://youtube.com/@leonvanzyl) proporciona una **introducción completa a la arquitectura Sequential Agent** y demuestra cómo gestionar estados personalizados para construir aplicaciones más dinámicas.

{% embed url="https://www.youtube.com/watch?v=6LbvgTbS0BE" %}

### Sequential vs. Multi Agents: ¿Qué característica de Flowise es adecuada para ti?

En este video, [<PERSON>](https://youtube.com/@leonvanzyl) examina dos soluciones diferentes en Flowise para crear proyectos multi-agente. Compara las **diferencias entre Sequential Agents y Multi Agents** recreando los mismos proyectos usando ambas técnicas.

{% embed url="https://www.youtube.com/watch?v=3ZmBq8_4vCs" %}

### Construye Apps Listas para Producción en Minutos: **Sequential Agents** de Flowise y n8n

En este video, [Wntrmute AI](https://www.youtube.com/@WntrmuteAI) demuestra cómo construir rápidamente una **aplicación lista para producción** en menos de 30 minutos combinando los **Sequential Agents** de Flowise y **n8n**.

{% embed url="https://www.youtube.com/watch?v=DA_0eOTYnmc" %}

### Cómo Construir una IA Auto-Mejorable con Agentic RAG y Flowise

En este video, [Leon](https://youtube.com/@leonvanzyl) te mostrará cómo construir una aplicación RAG auto-correctiva usando los Sequential Agents de FlowiseAI. Agentic RAG es un enfoque poderoso para crear soluciones de IA que pueden aprender y mejorar sus respuestas con el tiempo.

{% embed url="https://www.youtube.com/watch?v=SL77Ojbgy6U" %}
