---
description: Aprende cómo construir sistemas agénticos en Flowise
---

# Agentflows

## Introducción a los Sistemas Agénticos en Flowise

La sección Agentflows de Flowise proporciona una plataforma para construir sistemas basados en agentes que pueden interactuar con herramientas y fuentes de datos externas.

Actualmente, Flowise ofrece dos enfoques para diseñar estos sistemas: [**Multi-Agents**](#user-content-fn-1)[^1] y [**Sequential Agents**](#user-content-fn-2)[^2]. Estos enfoques proporcionan diferentes niveles de control y complejidad, permitiéndote elegir la mejor opción para tus necesidades.

<figure><img src="../../.gitbook/assets/agentflow.png" alt=""><figcaption><p>Flowise APP</p></figcaption></figure>

{% hint style="success" %}
Esta documentación explorará tanto el enfoque de Sequential Agent como el de Multi-Agent, explicando sus características y cómo pueden ser utilizados para construir diferentes tipos de flujos de trabajo conversacionales.
{% endhint %}

[^1]: Los **Multi-Agents**, construidos sobre la arquitectura de Sequential Agent, simplifican el proceso de construcción y gestión de equipos de agentes al preconfigurar elementos centrales y proporcionar una abstracción de más alto nivel.

[^2]: Los **Sequential Agents** proporcionan a los desarrolladores acceso directo a la estructura de flujo de trabajo subyacente, permitiendo un control granular sobre cada paso del flujo de conversación y ofreciendo máxima flexibilidad para construir aplicaciones conversacionales altamente personalizadas.
