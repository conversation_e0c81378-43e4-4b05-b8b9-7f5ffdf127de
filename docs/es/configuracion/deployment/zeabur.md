---
description: Learn how to deploy Flowise on Zeabur
---

# Zeabur

## Deploy usando Zeabur

1. Haz click en el botón de abajo para hacer deploy de Flowise en Zeabur

[![Deploy on Zeabur](https://zeabur.com/button.svg)](https://zeabur.com/templates/VZGGTI)

2. Haz click en **Deploy**

<figure><img src="../../.gitbook/assets/zeabur/1.png" alt=""><figcaption></figcaption></figure>

3. Selecciona **Configure**

<figure><img src="../../.gitbook/assets/zeabur/2.png" alt=""><figcaption></figcaption></figure>

4. Haz click en **Deploy**

<figure><img src="../../.gitbook/assets/zeabur/3.png" alt=""><figcaption></figcaption></figure>

5. Espera a que el deployment se complete

<figure><img src="../../.gitbook/assets/zeabur/4.png" alt=""><figcaption></figcaption></figure>

6. Hay una lista de environment variables que puedes configurar. Consulta [environment-variables.md](../environment-variables.md "mention")

¡Eso es todo! Ahora tienes Flowise desplegado en Zeabur [🎉](https://emojipedia.org/party-popper/)[🎉](https://emojipedia.org/party-popper/)

## Persistent Volume

Zeabur creará automáticamente un persistent volume para ti, así que no tienes que preocuparte por eso.
