---
description: Learn how to deploy Flowise on Sealos
---

# Sealos

## Deploy usando Sealos

1. Haz click en el botón de abajo para hacer deploy de Flowise en Sealos

[![Deploy on Sealos](https://sealos.io/Deploy-on-Sealos.svg)](https://template.sealos.io/deploy?templateName=flowise)

2. Haz click en **Deploy**

<figure><img src="../../.gitbook/assets/sealos/1.png" alt=""><figcaption></figcaption></figure>

3. Espera a que el deployment se complete

<figure><img src="../../.gitbook/assets/sealos/2.png" alt=""><figcaption></figcaption></figure>

4. Haz click en **Visit** para acceder a tu aplicación

<figure><img src="../../.gitbook/assets/sealos/3.png" alt=""><figcaption></figcaption></figure>

## Persistent Storage

Sealos creará automáticamente un persistent volume para ti, así que no tienes que preocuparte por eso.

Ahora intenta crear un flujo y guardarlo en Flowise. Luego intenta reiniciar el servicio o hacer redeploy, deberías poder ver el flujo que guardaste previamente.
