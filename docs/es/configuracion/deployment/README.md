---
description: Aprende cómo hacer deployment de Flowise a la cloud
---

# Deployment

***

Flowise está diseñado con una platform-agnostic architecture, asegurando compatibilidad con una amplia gama de deployment environments para adaptarse a tus necesidades de infrastructure.

## Local Machine

Para hacer deployment de Flowise localmente, sigue nuestra guía [Get Started](../../getting-started/).

## Modern Cloud Providers

Las modern cloud platforms priorizan la automation y se enfocan en developer workflows, simplificando el cloud management y ongoing maintenance.

Esto reduce la technical expertise necesaria, pero puede limitar el nivel de customization que tienes sobre la underlying infrastructure.

* [Elestio](https://elest.io/open-source/flowiseai)
* [Hugging Face](hugging-face.md)
* [Railway](railway.md)
* [Render](render.md)
* [Replit](replit.md)
* [RepoCloud](https://repocloud.io/details/?app_id=29)
* [Sealos](sealos.md)
* [Zeabur](zeabur.md)

## Established Cloud Providers

Los established cloud providers, por otro lado, requieren un mayor nivel de technical expertise para manage y optimize según tus specific needs.

Esta complexity, sin embargo, también otorga mayor flexibility y control sobre tu cloud environment.

* [AWS](aws.md)
* [Azure](azure.md)
* [DigitalOcean](digital-ocean.md)
* [GCP](gcp.md)
* [Kubernetes using Helm](https://artifacthub.io/packages/helm/cowboysysop/flowise)
