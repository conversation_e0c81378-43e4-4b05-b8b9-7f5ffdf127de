# Содержание

-   [Введение](README.md)
-   [Начало работы](getting-started/README.md)
-   [Возможности Universo Platformo](universo-platformo/README.md)
    -   [Система узлов UPDL](universo-platformo/updl-nodes/README.md)
        -   [Узлы пространства](universo-platformo/updl-nodes/space-nodes.md)
        -   [Узлы сущностей](universo-platformo/updl-nodes/entity-nodes.md)
        -   [Условные параметры](universo-platformo/updl-nodes/conditional-parameters.md)
        -   [Узлы компонентов](universo-platformo/updl-nodes/component-nodes.md)
        -   [Узлы действий](universo-platformo/updl-nodes/action-nodes.md)
        -   [Узлы событий](universo-platformo/updl-nodes/event-nodes.md)
        -   [Узлы данных](universo-platformo/updl-nodes/data-nodes.md)
    -   [Шаблоны MMOOMM](universo-platformo/mmoomm-templates/README.md)
        -   [PlayCanvas MMOOMM](universo-platformo/mmoomm-templates/playcanvas-mmoomm.md)
        -   [Шаблоны AR.js](universo-platformo/mmoomm-templates/arjs-templates.md)
        -   [Шаблоны A-Frame](universo-platformo/mmoomm-templates/aframe-templates.md)
    -   [Мультиплатформенный экспорт](universo-platformo/export/README.md)
        -   [Экспорт AR.js](universo-platformo/export/arjs-export.md)
        -   [Экспорт PlayCanvas](universo-platformo/export/playcanvas-export.md)
        -   [Экспорт A-Frame](universo-platformo/export/aframe-export.md)
    -   [Улучшенные системы ресурсов](universo-platformo/resources/README.md)
        -   [Управление ресурсами](universo-platformo/resources/resource-management.md)
        -   [Торговые системы](universo-platformo/resources/trading-systems.md)
        -   [Механики майнинга](universo-platformo/resources/mining-mechanics.md)
-   [Дорожная карта](roadmap/README.md)
    -   [Текущая архитектура](roadmap/current-architecture/README.md)
        -   [Существующие приложения](roadmap/current-architecture/existing-apps.md)
        -   [Анализ packages](roadmap/current-architecture/packages-analysis.md)
        -   [Паттерны интеграции](roadmap/current-architecture/integration-patterns.md)
    -   [Целевая архитектура](roadmap/target-architecture/README.md)
        -   [Приложения MMOOMM](roadmap/target-architecture/mmoomm-apps.md)
        -   [Базовые приложения платформы](roadmap/target-architecture/core-platform-apps.md)
        -   [Дизайн микросервисов](roadmap/target-architecture/microservices-design.md)
        -   [Стратегия оркестрации](roadmap/target-architecture/orchestration-strategy.md)
    -   [План реализации](roadmap/implementation-plan/README.md)
        -   [Фаза 1: MVP](roadmap/implementation-plan/phase-1-mvp.md)
        -   [Фаза 2: Базовые системы](roadmap/implementation-plan/phase-2-core.md)
        -   [Фаза 3: Продвинутые функции](roadmap/implementation-plan/phase-3-advanced.md)
        -   [Фаза 4: Экосистема](roadmap/implementation-plan/phase-4-ecosystem.md)
        -   [Стратегия миграции](roadmap/implementation-plan/migration-strategy.md)
    -   [Приложения](roadmap/applications/README.md)
        -   [Игровые механики](roadmap/applications/game-mechanics/README.md)
        -   [Социальные системы](roadmap/applications/social-systems/README.md)
        -   [Технические системы](roadmap/applications/technical-systems/README.md)
        -   [Платформенные приложения](roadmap/applications/platform-core/README.md)
    -   [Технические спецификации](roadmap/technical-specifications/README.md)
        -   [UI/UX Спецификации](roadmap/technical-specifications/ui-ux-specifications.md)
        -   [Дизайн базы данных](roadmap/technical-specifications/database-design.md)
        -   [Спецификации API](roadmap/technical-specifications/api-specifications.md)
        -   [Требования безопасности](roadmap/technical-specifications/security-requirements.md)
        -   [Показатели производительности](roadmap/technical-specifications/performance-targets.md)
    -   [Этапы и вехи](roadmap/milestones/README.md)
        -   [v0.22.0-alpha](roadmap/milestones/v0.22.0-alpha.md)
        -   [v0.25.0-beta](roadmap/milestones/v0.25.0-beta.md)
        -   [v1.0.0-release](roadmap/milestones/v1.0.0-release.md)
        -   [Будущие версии](roadmap/milestones/future-versions.md)
-   [Приложения](applications/README.md)
    -   [Система UPDL](applications/updl/README.md)
    -   [Система публикации](applications/publish/README.md)
    -   [Управление профилями](applications/profile/README.md)
    -   [Система аналитики](applications/analytics/README.md)
    -   [Система аутентификации](applications/auth/README.md)
-   [Руководство по участию](contributing/README.md)
    -   [Создание узлов](contributing/building-node.md)
-   [Справочник API](api-reference/README.md)
    -   [Ассистенты](api-reference/assistants.md)
    -   [Вложения](api-reference/attachments.md)
    -   [Сообщения чата](api-reference/chat-message.md)
    -   [Чатфлоу](api-reference/chatflows.md)
    -   [Хранилище документов](api-reference/document-store.md)
    -   [Обратная связь](api-reference/feedback.md)
    -   [Лиды](api-reference/leads.md)
    -   [Пинг](api-reference/ping.md)
    -   [Предсказание](api-reference/prediction.md)
    -   [Инструменты](api-reference/tools.md)
    -   [История обновлений](api-reference/upsert-history.md)
    -   [Переменные](api-reference/variables.md)
    -   [Векторное обновление](api-reference/vector-upsert.md)
-   [Справочник CLI](cli-reference/README.md)
    -   [Пользователь](cli-reference/user.md)
-   [Использование Universo Platformo](using-flowise/README.md)
    -   [Агентфлоу V2](using-flowise/agentflowv2.md)
    -   [Агентфлоу V1 (устаревает)](using-flowise/agentflowv1/README.md)
        -   [Мульти-агенты](using-flowise/agentflowv1/multi-agents.md)
        -   [Последовательные агенты](using-flowise/agentflowv1/sequential-agents/README.md)
            -   [Видео-уроки](using-flowise/agentflowv1/sequential-agents/video-tutorials.md)
    -   [Предсказание](using-flowise/prediction.md)
    -   [Потоковая передача](using-flowise/streaming.md)
    -   [Хранилища документов](using-flowise/document-stores.md)
    -   [Обновление](using-flowise/upsertion.md)
    -   [Аналитика](using-flowise/analytics/README.md)
        -   [Arize](using-flowise/analytics/arize.md)
        -   [Langfuse](using-flowise/analytics/langfuse.md)
        -   [Lunary](using-flowise/analytics/lunary.md)
        -   [Opik](using-flowise/analytics/opik.md)
        -   [Phoenix](using-flowise/analytics/phoenix.md)
    -   [Мониторинг](using-flowise/monitoring.md)
    -   [Встраивание](using-flowise/embed.md)
    -   [Загрузки](using-flowise/uploads.md)
    -   [Переменные](using-flowise/variables.md)
    -   [Рабочие пространства](using-flowise/workspaces.md)
    -   [Оценки](using-flowise/evaluations.md)
-   [Конфигурация](configuration/README.md)
    -   [Переменные окружения](configuration/environment-variables.md)
    -   [Развертывание](configuration/deployment/README.md)
        -   [AWS](configuration/deployment/aws.md)
        -   [Azure](configuration/deployment/azure.md)
        -   [GCP](configuration/deployment/gcp.md)
    -   [Авторизация](configuration/authorization/README.md)
        -   [Уровень приложения](configuration/authorization/app-level.md)
        -   [Потоки](configuration/authorization/chatflow-level.md)
    -   [Базы данных](configuration/databases.md)
        -   [AWS](configuration/deployment/aws.md)
        -   [Azure](configuration/deployment/azure.md)
        -   [Alibaba Cloud](https://aliyun-computenest.github.io/quickstart-flowise/)
        -   [Digital Ocean](configuration/deployment/digital-ocean.md)
        -   [Elestio](https://elest.io/open-source/flowiseai)
        -   [GCP](configuration/deployment/gcp.md)
        -   [Hugging Face](configuration/deployment/hugging-face.md)
        -   [Kubernetes с Helm](https://artifacthub.io/packages/helm/cowboysysop/flowise)
        -   [Railway](configuration/deployment/railway.md)
        -   [Render](configuration/deployment/render.md)
        -   [Replit](configuration/deployment/replit.md)
        -   [RepoCloud](https://repocloud.io/details/?app_id=29)
        -   [Sealos](configuration/deployment/sealos.md)
        -   [Zeabur](configuration/deployment/zeabur.md)
    -   [Переменные окружения](configuration/environment-variables.md)
    -   [Ограничение скорости](configuration/rate-limit.md)
    -   [Запуск Flowise за корпоративным прокси](configuration/running-flowise-behind-company-proxy.md)
    -   [SSO](configuration/sso.md)
    -   [Запуск Flowise с очередью](configuration/running-flowise-using-queue.md)
    -   [Запуск в продакшене](configuration/running-in-production.md)
-   [Интеграции](integrations/README.md)
    -   [LangChain](integrations/langchain/README.md)
        -   [Агенты](integrations/langchain/agents/README.md)
            -   [Конверсационный агент](integrations/langchain/agents/conversational-agent.md)
        -   [Цепочки](integrations/langchain/chains/README.md)
        -   [Инструменты](integrations/langchain/tools/README.md)
            -   [Калькулятор](integrations/langchain/tools/calculator.md)
            -   [Пользовательский инструмент](integrations/langchain/tools/custom-tool.md)
        -   [Векторные хранилища](integrations/langchain/vector-stores/README.md)
        -   [Память](integrations/langchain/memory/README.md)
        -   [Загрузчики документов](integrations/langchain/document-loaders/README.md)
        -   [Разделители текста](integrations/langchain/text-splitters/README.md)
            -   [Рекурсивный символьный разделитель](integrations/langchain/text-splitters/recursive-character-text-splitter.md)
    -   [LiteLLM Proxy](integrations/litellm/README.md)
    -   [LlamaIndex](integrations/llamaindex/README.md)
    -   [Утилиты](integrations/utilities/README.md)
        -   [Пользовательская JS функция](integrations/utilities/custom-js-function.md)
        -   [Установить/Получить переменную](integrations/utilities/set-get-variable.md)
        -   [Если-Иначе](integrations/utilities/if-else.md)
        -   [Заметка](integrations/utilities/sticky-note.md)
    -   [Внешние интеграции](integrations/3rd-party-platform-integration/README.md)
        -   [Zapier Zaps](integrations/3rd-party-platform-integration/zapier-zaps.md)
        -   [Open WebUI](integrations/3rd-party-platform-integration/open-webui.md)
        -   [Streamlit](integrations/3rd-party-platform-integration/streamlit.md)
-   [Примеры использования](use-cases/README.md)
    -   [Вопросы и ответы по множественным документам](use-cases/multiple-documents-qna.md)
    -   [Вопросы и ответы по SQL](use-cases/sql-qna.md)
-   [Руководство по миграции](migration-guide/README.md)

    -   [Миграция в облако](migration-guide/cloud-migration.md)
    -   [Руководство по миграции v1.3.0](migration-guide/v1.3.0-migration-guide.md)
    -   [Руководство по миграции v1.4.3](migration-guide/v1.4.3-migration-guide.md)
    -   [Руководство по миграции v2.1.4](migration-guide/v2.1.4-migration-guide.md)

-   [Учебники](tutorials/README.md)

    -   [RAG](tutorials/rag.md)
    -   [Агентный RAG](tutorials/agentic-rag.md)
    -   [SQL Агент](tutorials/sql-agent.md)
    -   [Агент как инструмент](tutorials/agent-as-tool.md)
    -   [Взаимодействие с API](tutorials/interacting-with-api.md)
    -   [Инструменты и MCP](tutorials/tools-and-mcp.md)
    -   [Структурированный вывод](tutorials/structured-output.md)
    -   [Человек в цикле](tutorials/human-in-the-loop.md)
    -   [Глубокое исследование](tutorials/deep-research.md)
    -   [Поддержка клиентов](tutorials/customer-support.md)
    -   [Супервизор и работники](tutorials/supervisor-and-workers.md)

-   [Устранение неполадок](troubleshooting/README.md)
-   [Руководство по миграции](migration-guide/README.md)

## Справочные материалы

-   [Глоссарий терминов](GLOSSARY.md)
-   [Статус перевода](TRANSLATION_STATUS.md)

## Universo Platformo

-   [GitHub Universo Platformo](https://github.com/VladimirLevadnij/universo-platformo-react)
-   [Оригинальный Flowise](https://github.com/FlowiseAI/Flowise)
-   [Облако Flowise](https://flowiseai.com/join)
