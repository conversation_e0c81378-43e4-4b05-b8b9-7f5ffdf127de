# Приложения Universo MMOOMM

## Краткое описание

Детальное описание специализированных приложений для реализации игровых механик Universo MMOOMM - космического MMO с экономической системой, основанной на добыче ресурсов, торговле между мирами и корпоративных взаимодействиях.

## Содержание

- [Игровые механики](#игровые-механики)
- [Социальные системы](#социальные-системы)
- [Технические системы](#технические-системы)
- [Интеграция с PlayCanvas](#интеграция-с-playcanvas)
- [Архитектура данных](#архитектура-данных)

## Игровые механики

### Resources Management System

#### resources-frt
**Назначение**: Фронтенд управления ресурсами и инвентарем

**Ключевые функции**:
- Отображение инвентаря с весом/объемом
- Управление 16 типами материалов
- Калькулятор плотности и массы
- Интерфейс передачи ресурсов между кораблями/станциями

**Технические детали**:
```typescript
interface ResourceInventory {
    playerId: string;
    location: {
        type: 'ship' | 'station' | 'storage';
        id: string;
    };
    materials: MaterialStack[];
    totalMass: number;
    totalVolume: number;
    capacity: {
        maxMass: number;
        maxVolume: number;
    };
}

interface MaterialStack {
    materialType: MaterialType;
    quantity: number;
    mass: number;
    volume: number;
}
```

#### resources-srv
**Назначение**: Бэкенд системы ресурсов с реалистичной физикой

**Ключевые функции**:
- API управления инвентарем
- Расчеты плотности материалов
- Валидация физических ограничений
- Система логов перемещения ресурсов

**16 типов материалов**:
1. **Hydrogen** (0.09 кг/м³) - Топливо для двигателей
2. **Helium** (0.18 кг/м³) - Охлаждающий агент
3. **Carbon** (2267 кг/м³) - Конструкционный материал
4. **Oxygen** (1.43 кг/м³) - Системы жизнеобеспечения
5. **Silicon** (2329 кг/м³) - Электроника и процессоры
6. **Iron** (7874 кг/м³) - Основной конструкционный металл
7. **Nickel** (8908 кг/м³) - Сплавы и покрытия
8. **Copper** (8960 кг/м³) - Электропроводка
9. **Silver** (10490 кг/м³) - Высокотехнологичная электроника
10. **Gold** (19300 кг/м³) - Премиум компоненты
11. **Platinum** (21450 кг/м³) - Катализаторы и двигатели
12. **Uranium** (19050 кг/м³) - Ядерное топливо
13. **Titanium** (4506 кг/м³) - Легкие прочные конструкции
14. **Aluminum** (2700 кг/м³) - Легкие компоненты
15. **Lithium** (534 кг/м³) - Батареи и энергохранилища
16. **Rare Earth** (7000 кг/м³) - Специализированные технологии

### Economy System

#### economy-frt
**Назначение**: Фронтенд экономической системы

**Ключевые функции**:
- Отображение курсов валют между мирами
- Интерфейс обмена Inmo
- Калькулятор прибыльности торговых маршрутов
- История транзакций и аналитика

#### economy-srv
**Назначение**: Бэкенд валюты Inmo и экономических операций

**Ключевые функции**:
- Управление балансами Inmo
- Динамическое ценообразование
- Межмировые переводы
- Экономическая аналитика

**Экономические системы миров**:
```typescript
interface WorldEconomy {
    worldId: 'kubio' | 'konkordo' | 'triumfo';
    baseCurrency: 'inmo';
    exchangeRates: {
        [materialType: string]: number; // Inmo за единицу материала
    };
    marketVolatility: number; // Коэффициент волатильности цен
    tradeVolume: number; // Объем торговли за период
}

// Kubio - Промышленный мир (высокий спрос на металлы)
// Konkordo - Технологический мир (высокий спрос на редкие элементы)
// Triumfo - Торговый мир (стабильные цены, низкие комиссии)
```

### Ships & Navigation System

#### ships-frt
**Назначение**: Фронтенд управления кораблями

**Ключевые функции**:
- Интерфейс флота игрока
- Конфигуратор кораблей
- Система модификаций и улучшений
- Мониторинг состояния и ремонта

#### ships-srv
**Назначение**: Бэкенд флота и кастомизации кораблей

**Ключевые функции**:
- API управления кораблями
- Система конфигураций и модулей
- Расчеты производительности
- Система повреждений и ремонта

**Типы кораблей**:
```typescript
interface ShipConfiguration {
    shipId: string;
    shipType: 'miner' | 'trader' | 'explorer' | 'fighter';
    modules: {
        engine: EngineModule;
        cargoHold: CargoModule;
        miningLaser?: MiningModule;
        shields?: ShieldModule;
        weapons?: WeaponModule[];
    };
    performance: {
        speed: number;
        cargoCapacity: number;
        miningEfficiency?: number;
        combatRating?: number;
    };
}
```

#### navigation-frt / navigation-srv
**Назначение**: Система навигации между мирами

**Ключевые функции**:
- Интерфейс звездных врат
- Планирование маршрутов
- Расчет времени и стоимости перелетов
- Система исследования новых систем

### Stations & Mining System

#### stations-frt / stations-srv
**Назначение**: Космические станции и производство

**Ключевые функции**:
- Строительство и модификация станций
- Производственные цепочки
- Система найма NPC рабочих
- Управление энергией и ресурсами

#### mining-frt / mining-srv
**Назначение**: Промышленный лазерный майнинг

**Ключевые функции**:
- Система автонаведения лазеров (50-100 единиц дальности)
- Визуализация красных лазерных лучей
- 3-секундные циклы добычи
- Автоматизированная симуляция сбора дронами

**Механика майнинга**:
```typescript
interface MiningOperation {
    shipId: string;
    targetAsteroid: {
        id: string;
        position: Vector3;
        composition: MaterialComposition;
        remainingMass: number;
    };
    laserConfiguration: {
        power: number; // Мощность лазера (влияет на скорость добычи)
        range: number; // Дальность (50-100 единиц)
        efficiency: number; // Эффективность добычи (0-1)
    };
    cycleTime: 3000; // 3 секунды на цикл
    autoTargeting: boolean;
    droneCollection: boolean;
}
```

## Социальные системы

### Corporations System

#### corporations-frt / corporations-srv
**Назначение**: Корпорации и организации игроков

**Ключевые функции**:
- Создание и управление корпорациями
- Иерархия ролей и прав доступа
- Корпоративные активы и финансы
- Система налогов и дивидендов

**Структура корпорации**:
```typescript
interface Corporation {
    id: string;
    name: string;
    description: string;
    foundedDate: Date;
    headquarters: {
        worldId: string;
        stationId?: string;
    };
    members: CorporationMember[];
    assets: {
        inmoBalance: number;
        ships: string[];
        stations: string[];
        resources: ResourceInventory;
    };
    roles: CorporationRole[];
}

interface CorporationRole {
    name: string;
    permissions: {
        canInviteMembers: boolean;
        canManageAssets: boolean;
        canAccessCorporateHangars: boolean;
        canDeclareWar: boolean;
        canMakeDiplomacy: boolean;
    };
}
```

### Diplomacy System

#### diplomacy-frt / diplomacy-srv
**Назначение**: Дипломатические отношения

**Ключевые функции**:
- Система союзов и враждебности
- Переговоры и договоры
- Объявление войн и перемирий
- Дипломатическая репутация

### Trading System

#### trading-frt / trading-srv
**Назначение**: Продвинутая торговая система

**Ключевые функции**:
- Торговые платформы и аукционы
- Система контрактов
- Логистика и доставка
- Рыночная аналитика и прогнозы

## Технические системы

### Enhanced Authentication

#### auth-enhanced-frt / auth-enhanced-srv
**Назначение**: Расширенная система авторизации

**Ключевые функции**:
- Интеграция игровых и платформенных аккаунтов
- Единый вход (SSO) между системами
- Верификация личности для корпораций
- Система репутации и доверия

**Архитектура авторизации**:
```typescript
interface UniversoUser {
    platformAccount: {
        id: string;
        email: string;
        role: 'user' | 'developer' | 'admin';
    };
    gameAccounts: {
        [worldId: string]: {
            playerId: string;
            characterName: string;
            reputation: number;
            corporationId?: string;
        };
    };
    verificationStatus: {
        emailVerified: boolean;
        phoneVerified: boolean;
        identityVerified: boolean; // Для корпоративных лидеров
    };
}
```

### Multiplayer System

#### multiplayer-frt / multiplayer-srv
**Назначение**: Реальное время мультиплеер

**Ключевые функции**:
- Синхронизация позиций кораблей
- Обработка столкновений и взаимодействий
- Система инстансов и зон
- Оптимизация сетевого трафика

**Сетевая архитектура**:
```typescript
interface MultiplayerState {
    worldId: string;
    players: {
        [playerId: string]: {
            position: Vector3;
            rotation: Quaternion;
            shipId: string;
            status: 'online' | 'mining' | 'trading' | 'combat';
            lastUpdate: number;
        };
    };
    entities: {
        asteroids: AsteroidState[];
        stations: StationState[];
        npcs: NPCState[];
    };
}
```

### Security & Monitoring

#### security-frt / security-srv
**Назначение**: Безопасность и защита от читов

**Ключевые функции**:
- Детекция читов и эксплойтов
- Система банов и предупреждений
- Мониторинг подозрительной активности
- Защита экономики от манипуляций

## Интеграция с PlayCanvas

### Template Engine Integration

Все MMOOMM приложения интегрируются с PlayCanvas через расширенный Template Engine:

```typescript
interface MMOOMMTemplate {
    name: 'universo-mmoomm';
    version: string;
    gameLogic: {
        resourceSystem: ResourceSystemConfig;
        economySystem: EconomySystemConfig;
        shipsSystem: ShipsSystemConfig;
        multiplayerSystem: MultiplayerSystemConfig;
    };
    assets: {
        ships: ShipAsset[];
        stations: StationAsset[];
        materials: MaterialAsset[];
        ui: UIAsset[];
    };
    scripts: {
        clientScripts: ClientScript[];
        serverScripts: ServerScript[];
    };
}
```

### Real-time Synchronization

```typescript
class PlayCanvasMMOOMMClient {
    private supabase: SupabaseClient;
    private gameState: MultiplayerState;
    
    async syncPlayerPosition(position: Vector3, rotation: Quaternion) {
        await this.supabase
            .from('player_positions')
            .upsert({
                player_id: this.playerId,
                world_id: this.worldId,
                x: position.x,
                y: position.y,
                z: position.z,
                qx: rotation.x,
                qy: rotation.y,
                qz: rotation.z,
                qw: rotation.w,
                timestamp: Date.now()
            });
    }
    
    subscribeToWorldEvents() {
        this.supabase
            .channel(`world:${this.worldId}`)
            .on('postgres_changes', {
                event: '*',
                schema: 'public',
                table: 'world_events'
            }, this.handleWorldEvent.bind(this))
            .subscribe();
    }
}
```

## Архитектура данных

### Схемы базы данных

```sql
-- Игровые миры и игроки
CREATE SCHEMA universo_worlds;
CREATE TABLE universo_worlds.players (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id),
    world_id VARCHAR(50),
    character_name VARCHAR(100),
    position_x DECIMAL(15,6),
    position_y DECIMAL(15,6),
    position_z DECIMAL(15,6),
    current_ship_id UUID,
    inmo_balance DECIMAL(15,2),
    reputation INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Ресурсы и материалы
CREATE SCHEMA universo_resources;
CREATE TABLE universo_resources.material_types (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100),
    density_kg_m3 DECIMAL(10,2),
    base_value_inmo DECIMAL(10,2),
    rarity_factor DECIMAL(3,2)
);

-- Корабли и флот
CREATE SCHEMA universo_ships;
CREATE TABLE universo_ships.ships (
    id UUID PRIMARY KEY,
    player_id UUID REFERENCES universo_worlds.players(id),
    ship_type VARCHAR(50),
    name VARCHAR(100),
    configuration JSONB,
    current_world VARCHAR(50),
    position_x DECIMAL(15,6),
    position_y DECIMAL(15,6),
    position_z DECIMAL(15,6),
    health_percentage DECIMAL(5,2) DEFAULT 100.00
);

-- Корпорации
CREATE SCHEMA universo_corporations;
CREATE TABLE universo_corporations.corporations (
    id UUID PRIMARY KEY,
    name VARCHAR(100) UNIQUE,
    description TEXT,
    founded_date TIMESTAMP DEFAULT NOW(),
    headquarters_world VARCHAR(50),
    inmo_treasury DECIMAL(15,2) DEFAULT 0
);
```

## Связанные страницы

- [Базовые приложения платформы](core-platform-apps.md)
- [Дизайн микросервисов](microservices-design.md)
- [Фаза 1: MVP](../implementation-plan/phase-1-mvp.md)
- [Технические спецификации](../technical-specifications/README.md)

## Статус разработки

- [x] Проектирование игровых механик
- [x] Определение архитектуры данных
- [/] Создание технических спецификаций
- [ ] Начало разработки MVP
- [ ] Интеграция с PlayCanvas

---
*Последнее обновление: 5 августа 2025*
