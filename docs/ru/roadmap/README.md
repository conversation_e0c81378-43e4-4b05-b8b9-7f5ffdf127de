# Дорожная карта Universo Platformo

## Краткое описание

Дорожная карта определяет стратегическое развитие Universo Platformo от текущего Alpha статуса до полнофункциональной платформы для создания Universo MMOOMM и других 3D/AR/VR приложений. Документ описывает архитектурную эволюцию от 6 существующих приложений до комплексной экосистемы из 20+ микросервисов.

## Содержание

- [Текущая архитектура](#текущая-архитектура)
- [Целевая архитектура](#целевая-архитектура)
- [План реализации](#план-реализации)
- [Приложения](#приложения)
- [Технические спецификации](#технические-спецификации)
- [Этапы и вехи](#этапы-и-вехи)

## Текущая архитектура

**Статус**: Alpha достигнут (v0.21.0-alpha, июль 2025)

Universo Platformo в настоящее время состоит из 6 рабочих приложений, построенных поверх базы Flowise 2.2.8:

### Существующие приложения

```
apps/
├── updl/                # Система узлов UPDL (7 высокоуровневых узлов)
├── publish-frt/         # Фронтенд публикации (AR.js, PlayCanvas)
├── publish-srv/         # Бэкенд публикации (workspace пакет)
├── profile-frt/         # Фронтенд профилей пользователей
├── profile-srv/         # Бэкенд профилей (workspace пакет)
└── analytics-frt/       # Фронтенд аналитики квизов
```

### Ключевые достижения

- ✅ **Система UPDL**: 7 абстрактных узлов (Space, Entity, Component, Event, Action, Data, Universo)
- ✅ **Мультитехнологический экспорт**: AR.js (продакшн), PlayCanvas (готов)
- ✅ **Шаблонная архитектура**: Переиспользуемые шаблоны экспорта
- ✅ **MMOOMM MVP**: Базовый космический MMO с лазерным майнингом
- ✅ **Система ресурсов**: Реалистичная физика материалов с плотностью

## Целевая архитектура

**Видение**: Микросервисная экосистема из 20+ приложений для создания полнофункционального Universo MMOOMM

### Категории приложений

1. **Игровые механики** (8 приложений)
   - Ресурсы, экономика, корабли, станции, майнинг, навигация

2. **Социальные системы** (6 приложений)
   - Корпорации, дипломатия, торговля, коммуникации

3. **Технические системы** (6 приложений)
   - Расширенная авторизация, мультиплеер, безопасность, аналитика

4. **Платформенные приложения** (4 приложения)
   - Движок рабочих процессов, реестр узлов, API Gateway, шаблоны

### Архитектурные принципы

- **Микросервисная архитектура**: Каждое приложение как независимый сервис
- **API-first подход**: Все взаимодействия через четко определенные API
- **Event-driven архитектура**: Асинхронная связь через Supabase Realtime
- **Workspace пакеты**: Переиспользуемые компоненты между приложениями

## План реализации

### Фаза 1: MVP Universo MMOOMM (v0.22.0-alpha) - Q2 2025

**Цель**: Создание базового игрового функционала

**Приоритетные приложения**:
- `resources-frt/srv` - Система ресурсов с плотностью материалов
- `ships-frt/srv` - Управление кораблями
- `economy-frt/srv` - Базовая экономика между мирами

**Критерии готовности**:
- [ ] Работающий MVP с базовыми игровыми механиками
- [ ] Интеграция с PlayCanvas шаблоном
- [ ] Экономика между 3 мирами (Kubio, Konkordo, Triumfo)

### Фаза 2: Базовые системы (v0.25.0-beta) - Q3 2025

**Цель**: Социальные и технические системы

**Приложения**:
- `corporations-frt/srv` - Корпорации и организации
- `auth-enhanced-frt/srv` - Расширенная авторизация
- `multiplayer-frt/srv` - Реальное время мультиплеер

### Фаза 3: Продвинутые функции (v1.0.0) - Q4 2025

**Цель**: Полная экосистема игровых механик

**Приложения**:
- `stations-frt/srv` - Космические станции
- `trading-frt/srv` - Продвинутая торговля
- `diplomacy-frt/srv` - Дипломатические отношения

### Фаза 4: Экосистема (v1.5.0+) - 2026+

**Цель**: Микросервисная платформа

**Приложения**:
- `workflow-engine-srv` - Движок Chatflow
- `node-registry-srv` - Реестр узлов
- `api-gateway-srv` - API Gateway

## Приложения

### Игровые механики

Приложения, реализующие основные игровые механики Universo MMOOMM:

- **Ресурсы**: Система с 16 типами материалов и реалистичной физикой
- **Экономика**: Валюта Inmo с разными экономическими системами
- **Корабли**: Управление флотом с кастомизацией
- **Станции**: Космические базы и производство
- **Майнинг**: Промышленный лазерный майнинг
- **Навигация**: Звездные врата между мирами

### Социальные системы

Приложения для взаимодействия игроков:

- **Корпорации**: Организации с иерархией ролей
- **Дипломатия**: Союзы, конфликты, переговоры
- **Торговля**: Аукционы, контракты, логистика
- **Коммуникации**: Внутриигровая связь и форумы

### Технические системы

Приложения для технической поддержки платформы:

- **Авторизация**: Интеграция игровых и платформенных аккаунтов
- **Мультиплеер**: Синхронизация в реальном времени
- **Безопасность**: Защита от читов и эксплойтов
- **Аналитика**: Метрики и поведенческий анализ

## Технические спецификации

### Технологический стек

- **Frontend**: React + TypeScript + Material-UI
- **Backend**: Node.js + Express + TypeScript
- **База данных**: Supabase (PostgreSQL)
- **Реальное время**: Supabase Realtime
- **Пакетный менеджер**: PNPM workspaces
- **3D движки**: PlayCanvas, AR.js

### Архитектурные паттерны

- **API Gateway**: Единая точка входа для всех запросов
- **Event Bus**: Асинхронная связь через Supabase Realtime
- **Workspace пакеты**: Переиспользуемые компоненты
- **Template Engine**: Генерация кода для разных платформ

## Этапы и вехи

### Ближайшие релизы

- **v0.22.0-alpha** (Q2 2025): MVP игровых механик
- **v0.25.0-beta** (Q3 2025): Социальные системы
- **v1.0.0** (Q4 2025): Первый стабильный релиз

### Долгосрочные цели

- **v1.5.0** (2026): Микросервисная архитектура
- **v2.0.0** (2027): Интеграция с блокчейном TON
- **v3.0.0** (2028): Kiberplano функциональность

## Связанные страницы

- [Текущая архитектура](current-architecture/README.md)
- [Целевая архитектура](target-architecture/README.md)
- [План реализации](implementation-plan/README.md)
- [Приложения](applications/README.md)
- [Технические спецификации](technical-specifications/README.md)
- [Этапы и вехи](milestones/README.md)

## Статус реализации

- [x] Анализ текущей архитектуры
- [x] Планирование целевой архитектуры
- [/] Создание документации
- [ ] Начало реализации Фазы 1

---
*Последнее обновление: 5 августа 2025*
